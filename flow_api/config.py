"""
Configuration module for flow_api.

This module handles loading and validating environment variables required for the library.
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

from flow_api.exceptions.config_error import ConfigError


class Config:
    """Configuration manager for flow_api.
    
    This class is responsible for loading and validating environment variables
    required for authenticating with the AI API.
    """
    
    _instance = None
    
    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the configuration manager."""
        if self._initialized:
            return
            
        # Load environment variables from .env file if present
        load_dotenv()
        
        # Required environment variables
        self._client_id = os.getenv("FLOW_API_CLIENT_ID")
        self._client_secret = os.getenv("FLOW_API_CLIENT_SECRET")
        self._app_to_access = os.getenv("FLOW_API_APP_TO_ACCESS")
        self._flow_tenant = os.getenv("FLOW_API_TENANT")

        # Optional configuration
        self._token_cache_duration = 3500  # ~1 hour

        # Logging configuration
        self._console_log_level = self._get_log_level_from_env("FLOW_API_CONSOLE_LOG_LEVEL", logging.CRITICAL+1)
        self._file_log_level = self._get_log_level_from_env("FLOW_API_FILE_LOG_LEVEL", logging.DEBUG)
        self._log_file_path = os.getenv("FLOW_API_LOG_FILE_PATH")

        # Fixed URLs (not configurable by users)
        self._token_url = "https://flow.ciandt.com/auth-engine-api/v1/api-key/token"
        self._api_base_url = "https://flow.ciandt.com/ai-orchestration-api/v1"
        self._capabilities_url = "https://flow.ciandt.com/ai-orchestration-api/v2"

        # API Endpoint paths for each provider and capability
        self._endpoints = {
            "azure_openai": {
                "chat": "/openai/chat/completions",
                "embedding": "/openai/embeddings",
                "image_generation": "/openai/image-generator/generate?api-version=2024-02-01",
                "image_recognition": "/openai/chat/completions",
                "speech": "/openai/speechtotext/fast_transcription?api-version=2024-11-15"
            },
            "amazon_bedrock": {
                "chat": "/bedrock/invoke",
                "embedding": "/bedrock/invoke",
                "image_recognition": "/bedrock/invoke"
            },
            "google_gemini": {
                "chat": "/google/generateContent",
                "embedding": "/google/predict",
                "image_recognition": "/google/generateContent"
            },
            "azure_foundry": {
                "chat": "/foundry/chat/completions"
            }
        }

        self._initialized = True

    def _get_log_level_from_env(self, env_var: str, default_level: int) -> int:
        """Get log level from environment variable.

        Args:
            env_var: Environment variable name.
            default_level: Default log level if env var is not set or invalid.

        Returns:
            Log level as integer.
        """
        level_str = os.getenv(env_var, "").upper()
        level_mapping = {
            "ALL": logging.DEBUG,
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL,
            "OFF": logging.CRITICAL + 1,  # Effectively disables logging
            "": logging.CRITICAL + 1,
        }

        return level_mapping.get(level_str, default_level)

    def validate(self) -> None:
        """Validate that all required configuration values are present.
        
        Raises:
            ConfigError: If any required configuration value is missing.
        """
        missing = []
        
        if not self._client_id:
            missing.append("FLOW_API_CLIENT_ID")
        if not self._client_secret:
            missing.append("FLOW_API_CLIENT_SECRET")
        if not self._app_to_access:
            missing.append("FLOW_API_APP_TO_ACCESS")
        if not self._flow_tenant:
            missing.append("FLOW_API_TENANT")
            
        if missing:
            raise ConfigError(f"Missing required environment variables: {', '.join(missing)}")
    
    @property
    def client_id(self) -> str:
        """Get the client ID."""
        self.validate()
        # Type assertion: validate() ensures this is not None
        assert self._client_id is not None
        return self._client_id

    @property
    def client_secret(self) -> str:
        """Get the client secret."""
        self.validate()
        # Type assertion: validate() ensures this is not None
        assert self._client_secret is not None
        return self._client_secret

    @property
    def app_to_access(self) -> str:
        """Get the app to access."""
        self.validate()
        # Type assertion: validate() ensures this is not None
        assert self._app_to_access is not None
        return self._app_to_access

    @property
    def flow_tenant(self) -> str:
        """Get the flow tenant."""
        self.validate()
        # Type assertion: validate() ensures this is not None
        assert self._flow_tenant is not None
        return self._flow_tenant
    
    @property
    def token_cache_duration(self) -> int:
        """Get the token cache duration in seconds."""
        return self._token_cache_duration
    
    @property
    def token_url(self) -> str:
        """Get the token URL."""
        return self._token_url
    
    @property
    def api_base_url(self) -> str:
        """Get the API base URL."""
        return self._api_base_url

    @property
    def capabilities_url(self) -> str:
        """Get the capabilities URL."""
        return self._capabilities_url

    @property
    def console_log_level(self) -> int:
        """Get the console log level."""
        return self._console_log_level

    @property
    def file_log_level(self) -> int:
        """Get the file log level."""
        return self._file_log_level

    @property
    def log_file_path(self) -> Optional[str]:
        """Get the log file path."""
        return self._log_file_path

    def get_endpoint_path(self, provider: str, capability: str) -> str:
        """Get the endpoint path for a specific provider and capability.

        Args:
            provider: The provider name (e.g., 'azure_openai', 'amazon_bedrock').
            capability: The capability name (e.g., 'chat', 'embedding').

        Returns:
            The endpoint path for the specified provider and capability.

        Raises:
            ConfigError: If the provider or capability is not found.
        """
        if provider not in self._endpoints:
            raise ConfigError(f"Unknown provider: {provider}")

        provider_endpoints = self._endpoints[provider]
        if capability not in provider_endpoints:
            raise ConfigError(f"Unknown capability '{capability}' for provider '{provider}'")

        return provider_endpoints[capability]
    
    def as_dict(self) -> Dict[str, Any]:
        """Get the configuration as a dictionary."""
        return {
            "client_id": self._client_id,
            "client_secret": self._client_secret,
            "app_to_access": self._app_to_access,
            "flow_tenant": self._flow_tenant,
            "token_cache_duration": self._token_cache_duration,
            "token_url": self._token_url,
            "api_base_url": self._api_base_url,
            "capabilities_url": self._capabilities_url,
            "console_log_level": self._console_log_level,
            "file_log_level": self._file_log_level,
            "log_file_path": self._log_file_path,
            "endpoints": self._endpoints
        }