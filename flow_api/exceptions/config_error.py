"""
Configuration error exception.
"""

class ConfigError(Exception):
    """Exception raised for configuration errors.
    
    Attributes:
        message -- explanation of the error
    """
    
    def __init__(self, message: str):
        """Initialize the exception.
        
        Args:
            message: The error message.
        """
        self.message = message
        super().__init__(message)