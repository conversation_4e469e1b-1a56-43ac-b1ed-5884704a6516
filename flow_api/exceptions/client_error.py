"""
FlowAPIClient error exception.
"""

class ClientError(Exception):
    """Exception raised for client errors.
    
    Attributes:
        message -- explanation of the error
        status_code -- HTTP status code if applicable
        activity_id -- activity ID for troubleshooting if provided
    """
    
    def __init__(self, message: str, status_code: int | None = None, activity_id: str | None = None):
        """Initialize the exception.
        
        Args:
            message: The error message.
            status_code: The HTTP status code if applicable.
            activity_id: The activity ID for troubleshooting if provided.
        """
        self.message = message
        self.status_code = status_code
        self.activity_id = activity_id
        super().__init__(self._format_message())
    
    def _format_message(self) -> str:
        """Format the error message.
        
        Returns:
            A formatted error message.
        """
        parts = [self.message]
        
        if self.status_code:
            parts.append(f"Status code: {self.status_code}")
        
        if self.activity_id:
            parts.append(f"Activity ID: {self.activity_id}")
        
        return " | ".join(parts)