"""
Model registry module for flow_api.

This module provides a registry of available AI models.
"""

from typing import List, Dict, Optional, Any, Set

from flow_api.core.models import AIModel, ModelProvider
from flow_api.core.capabilities import Capability
from flow_api.exceptions.client_error import ClientError
from flow_api.adapters.outbound.auth.token_manager import TokenManager
from flow_api.utils.logging import get_logger


logger = get_logger(__name__)


class ModelRegistry:
    """Registry of available AI models.
    
    This class is responsible for loading and providing access to available AI models.
    """
    
    _instance = None
    
    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super(ModelRegistry, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the model registry."""
        if self._initialized:
            return

        self._models: Dict[str, AIModel] = {}
        self._raw_models_data: Optional[Dict[str, Any]] = None
        self._token_manager = TokenManager()

        # Load models from cache or API
        self._load_models()
        self._initialized = True

    def _load_models(self) -> None:
        """Load models from cache or fetch from API.

        Raises:
            ClientError: If models cannot be loaded.
        """
        try:
            # First try to get models from cache
            cached_models = self._token_manager.get_cached_models()

            if cached_models:
                logger.debug("Loading models from cache")
                self._load_models_from_cache(cached_models)
                return

            # If no cached models, fetch from API
            logger.info("No cached models found, fetching from API")
            try:
                models_data = self._token_manager.refresh_models()
                # Sanitize and cache the models data
                sanitized_data = self._sanitize_models_data(models_data)
                self._token_manager._cache.cache_models(sanitized_data, self._token_manager._config.token_cache_duration)
                self._process_models_data(sanitized_data, from_cache=False)
            except Exception as e:
                logger.error(f"Failed to fetch models from API: {str(e)}")
                raise ClientError(f"Failed to load models: {str(e)}")

        except Exception as e:
            logger.error(f"Failed to load models: {str(e)}")
            raise ClientError(f"Failed to load models: {str(e)}")

    def _sanitize_models_data(self, api_response: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize models data by inferring missing capabilities.

        This method processes the raw API response and adds missing capabilities
        based on model names. This is done only once when fetching from API,
        not when loading from cache.

        Args:
            api_response: Raw API response containing supported models.

        Returns:
            Sanitized API response with inferred capabilities.
        """
        sanitized_response = api_response.copy()
        supported_models = sanitized_response.get('supportedModels', {})

        for _, models_list in supported_models.items():
            for model_data in models_list:
                capabilities = model_data.get('capabilities', [])

                # If capabilities is empty, infer from model name
                if not capabilities:
                    model_name = model_data.get('name', '')
                    inferred_capabilities = self._infer_capabilities_from_name(model_name)
                    model_data['capabilities'] = [cap.value for cap in inferred_capabilities]
                    logger.debug(f"Inferred capabilities for {model_name}: {[cap.value for cap in inferred_capabilities]}")

        return sanitized_response

    def _infer_capabilities_from_name(self, model_name: str) -> List[Capability]:
        """Infer capabilities from model name when capabilities list is empty.

        Args:
            model_name: The name of the model.

        Returns:
            A list of inferred capabilities.
        """
        model_name_lower = model_name.lower()

        # Image generation models
        if any(keyword in model_name_lower for keyword in ['dall-e', 'dalle', 'image-gen']):
            return [Capability.IMAGE_GENERATION]

        # Speech-to-text models
        if any(keyword in model_name_lower for keyword in ['whisper', 'speech', 'transcrib']):
            return [Capability.SPEECH_TO_TEXT]

        # Text embedding models
        if any(keyword in model_name_lower for keyword in ['embedding', 'embed', 'vector']):
            return [Capability.TEXT_EMBEDDING]

        # Default to chat conversation for unknown models
        return [Capability.CHAT_CONVERSATION]

    def _load_models_from_cache(self, cached_data: Dict[str, Any]) -> None:
        """Load models directly from cache without processing.

        This method loads pre-processed models from cache, avoiding
        the sanitization step that was already done when caching.

        Args:
            cached_data: Pre-processed API response from cache.
        """
        # Store the raw API response
        self._raw_models_data = cached_data

        # Clear existing models
        self._models.clear()

        # Extract supported models from the cached response
        supported_models = cached_data.get('supportedModels', {})

        # Map API provider names to our ModelProvider enum values
        provider_mapping = {
            'azureOpenai': 'azure-openai',
            'azureFoundry': 'azure-foundry',
            'googleGemini': 'google-gemini',
            'amazonBedrock': 'amazon-bedrock',
            'azureAISpeech': 'azure-ai-speech'
        }

        # Track processed models to avoid duplicates (ignore region differences)
        processed_models = set()
        model_count = 0

        for api_provider_name, models_list in supported_models.items():
            # Map the API provider name to our enum value
            provider_name = provider_mapping.get(api_provider_name, api_provider_name.lower())

            for model_data in models_list:
                try:
                    model_name = model_data.get('name')
                    if not model_name:
                        continue

                    # Create unique identifier ignoring region (provider + name)
                    model_identifier = f"{provider_name}:{model_name}"

                    # Skip if we already processed this model
                    if model_identifier in processed_models:
                        continue

                    # Add provider field to model data
                    model_data_with_provider = model_data.copy()
                    model_data_with_provider['provider'] = provider_name

                    # Create an AIModel instance (data is already sanitized)
                    model = AIModel.from_dict(model_data_with_provider)

                    # Add to the registry using model name as key
                    self._models[model.name] = model
                    processed_models.add(model_identifier)
                    model_count += 1

                except Exception as e:
                    logger.warning(f"Failed to load cached model {model_data.get('name', 'unknown')}: {str(e)}")
                    continue

        logger.debug(f"Successfully loaded {model_count} unique models from cache")

    def _process_models_data(self, api_response: Dict[str, Any], from_cache: bool = False) -> None:
        """Process models data from API response.

        Args:
            api_response: API response containing supported models.
            from_cache: Whether the data comes from cache or API.
        """
        # Store the raw API response
        self._raw_models_data = api_response

        # Clear existing models
        self._models.clear()

        # Extract supported models from the API response
        supported_models = api_response.get('supportedModels', {})

        # Map API provider names to our ModelProvider enum values
        provider_mapping = {
            'azureOpenai': 'azure-openai',
            'azureFoundry': 'azure-foundry',
            'googleGemini': 'google-gemini',
            'amazonBedrock': 'amazon-bedrock',
            'azureAISpeech': 'azure-ai-speech'
        }

        # Track processed models to avoid duplicates (ignore region differences)
        processed_models = set()
        model_count = 0

        for api_provider_name, models_list in supported_models.items():
            # Map the API provider name to our enum value
            provider_name = provider_mapping.get(api_provider_name, api_provider_name.lower())

            for model_data in models_list:
                try:
                    model_name = model_data.get('name')
                    if not model_name:
                        continue

                    # Create unique identifier ignoring region (provider + name)
                    model_identifier = f"{provider_name}:{model_name}"

                    # Skip if we already processed this model
                    if model_identifier in processed_models:
                        logger.debug(f"Skipping duplicate model: {model_name} (different region)")
                        continue

                    # Add provider field to model data
                    model_data_with_provider = model_data.copy()
                    model_data_with_provider['provider'] = provider_name

                    # Create an AIModel instance
                    model = AIModel.from_dict(model_data_with_provider)

                    # Add to the registry using model name as key
                    self._models[model.name] = model
                    processed_models.add(model_identifier)
                    model_count += 1

                except Exception as e:
                    logger.warning(f"Failed to process model {model_data.get('name', 'unknown')}: {str(e)}")
                    continue

            logger.debug(f"Provider {api_provider_name}: {len(models_list)} models (before deduplication)")

        # Log appropriate message based on data source
        if from_cache:
            logger.debug(f"Successfully loaded {model_count} unique models from cache")
        else:
            logger.debug(f"Successfully processed and stored {model_count} unique models from API")

    def refresh_models(self) -> None:
        """Refresh models from the API.

        Raises:
            ClientError: If models cannot be refreshed.
        """
        try:
            logger.info("Refreshing models from API")
            models_data = self._token_manager.refresh_models()
            # Sanitize and cache the models data
            sanitized_data = self._sanitize_models_data(models_data)
            self._token_manager._cache.cache_models(sanitized_data, self._token_manager._config.token_cache_duration)
            self._process_models_data(sanitized_data, from_cache=False)
        except Exception as e:
            logger.error(f"Failed to refresh models: {str(e)}")
            raise ClientError(f"Failed to refresh models: {str(e)}")

    def get_raw_models_data(self) -> Optional[Dict[str, Any]]:
        """Get the raw models data from the API.

        Returns:
            The raw API response containing models data, or None if not loaded.
        """
        return self._raw_models_data

    def get_all_models(self) -> List[AIModel]:
        """Get all available models.

        Returns:
            A list of all available models.
        """
        return list(self._models.values())

    def get_model(self, model_name: str) -> Optional[AIModel]:
        """Get a model by name.

        Args:
            model_name: The name of the model.

        Returns:
            The model if found, None otherwise.
        """
        return self._models.get(model_name)
    
    def get_models_by_provider(self, provider: ModelProvider) -> List[AIModel]:
        """Get all models from a specific provider.

        Args:
            provider: The provider to filter by.

        Returns:
            A list of models from the specified provider.
        """
        return [model for model in self._models.values() if model.provider == provider]

    def get_models_by_capability(self, capability: Capability) -> List[AIModel]:
        """Get all models with a specific capability.

        Args:
            capability: The capability to filter by.

        Returns:
            A list of models with the specified capability.
        """
        return [model for model in self._models.values() if model.has_capability(capability)]

    def get_models_by_capabilities(self, capabilities: List[Capability]) -> List[AIModel]:
        """Get all models with all the specified capabilities.

        Args:
            capabilities: The capabilities to filter by.

        Returns:
            A list of models with all the specified capabilities.
        """
        return [model for model in self._models.values() if model.has_all_capabilities(capabilities)]

    def get_available_capabilities(self) -> Set[Capability]:
        """Get all available capabilities across all models.

        Returns:
            A set of all available capabilities.
        """
        capabilities = set()
        for model in self._models.values():
            capabilities.update(model.capabilities)
        return capabilities