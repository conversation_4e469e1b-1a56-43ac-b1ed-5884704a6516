"""
Models module for flow_api.

This module defines the core domain models for AI models.
"""

from enum import Enum
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field

from flow_api.core.capabilities import Capability
from flow_api.utils.logging import get_logger, log_and_raise


logger = get_logger(__name__)

class ModelProvider(str, Enum):
    """Enum representing AI model providers."""

    AZURE_OPENAI = "azure-openai"
    AZURE_FOUNDRY = "azure-foundry"
    GOOGLE_GEMINI = "google-gemini"
    AMAZON_BEDROCK = "amazon-bedrock"
    AZURE_AI_SPEECH = "azure-ai-speech"
    
    @classmethod
    def from_str(cls, provider: str) -> 'ModelProvider':
        """Convert a string to a ModelProvider enum value.
        
        Args:
            provider: The provider string.
            
        Returns:
            The corresponding ModelProvider enum value.
            
        Raises:
            ValueError: If the provider string is not valid.
        """
        for prov in cls:
            if prov.value == provider:
                return prov
        log_and_raise(logger, f"Invalid provider: {provider}", ValueError, "debug")


@dataclass
class AIModel:
    """Class representing an AI model from the Flow API.

    Attributes:
        name: The name of the model.
        provider: The provider of the model.
        directory: The directory/deployment name for the model.
        region: The region where the model is deployed.
        input_tokens: The maximum number of input tokens supported by the model.
        capabilities: The capabilities supported by the model.
        family: The family of the model if applicable.
    """

    name: str
    provider: ModelProvider
    directory: str
    region: str
    input_tokens: int
    capabilities: List[Capability] = field(default_factory=list)
    family: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AIModel':
        """Create an AIModel instance from a dictionary.

        Args:
            data: The dictionary containing model data from the API.

        Returns:
            An AIModel instance.
        """
        provider = ModelProvider.from_str(data["provider"])
        capabilities = Capability.list_from_strs(data.get("capabilities", []))

        return cls(
            name=data["name"],
            provider=provider,
            directory=data["directory"],
            region=data["region"],
            input_tokens=data["inputTokens"],
            capabilities=capabilities,
            family=data.get("family")
        )


    
    def has_capability(self, capability: Capability) -> bool:
        """Check if the model has a specific capability.
        
        Args:
            capability: The capability to check.
            
        Returns:
            True if the model has the capability, False otherwise.
        """
        return capability in self.capabilities
    
    def has_all_capabilities(self, capabilities: List[Capability]) -> bool:
        """Check if the model has all the specified capabilities.
        
        Args:
            capabilities: The capabilities to check.
            
        Returns:
            True if the model has all the capabilities, False otherwise.
        """
        return all(cap in self.capabilities for cap in capabilities)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the AIModel to a dictionary.

        Returns:
            A dictionary representation of the AIModel.
        """
        result = {
            "name": self.name,
            "provider": self.provider.value,
            "directory": self.directory,
            "region": self.region,
            "input_tokens": self.input_tokens,
            "capabilities": [cap.value for cap in self.capabilities],
        }

        if self.family is not None:
            result["family"] = self.family

        return result