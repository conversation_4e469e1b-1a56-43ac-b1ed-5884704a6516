"""
Capabilities module for flow_api.

This module defines the capabilities supported by AI models.
"""

from enum import Enum
from typing import List
from flow_api.utils.logging import get_logger, log_and_raise


logger = get_logger(__name__)


class Capability(str, Enum):
    """Enum representing capabilities supported by AI models."""

    STREAMING = "streaming"
    SYSTEM_INSTRUCTION = "system-instruction"
    CHAT_CONVERSATION = "chat-conversation"
    IMAGE_RECOGNITION = "image-recognition"
    TEXT_EMBEDDING = "text-embedding"
    IMAGE_GENERATION = "image-generation"
    SPEECH_TO_TEXT = "speech-to-text"
    
    @classmethod
    def from_str(cls, capability: str) -> 'Capability':
        """Convert a string to a Capability enum value.
        
        Args:
            capability: The capability string.
            
        Returns:
            The corresponding Capability enum value.
            
        Raises:
            ValueError: If the capability string is not valid.
        """
        for cap in cls:
            if cap.value == capability:
                return cap
        log_and_raise(logger, f"Invalid capability: {capability}", ValueError, "debug")
    
    @classmethod
    def list_from_strs(cls, capabilities: List[str]) -> List['Capability']:
        """Convert a list of capability strings to Capability enum values.
        
        Args:
            capabilities: The list of capability strings.
            
        Returns:
            The corresponding list of Capability enum values.
        """
        return [cls.from_str(cap) for cap in capabilities]