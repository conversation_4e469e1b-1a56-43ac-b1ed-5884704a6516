"""
flow_api - A Python library for interacting with the Flow AI models

This library provides a clean and intuitive interface to interact with available AI models
from different providers through a unified API.
"""

__version__ = "1.0.0"

# Main client interface
from flow_api.adapters.inbound.client import FlowAPIClient

# Core domain models
from flow_api.core.capabilities import Capability
from flow_api.core.models import AIModel, ModelProvider
from flow_api.core.model_registry import ModelRegistry

# Common exceptions
from flow_api.exceptions.client_error import ClientError
from flow_api.exceptions.config_error import ConfigError
from flow_api.exceptions.auth_error import AuthError

__all__ = [
    # Main client
    "FlowAPIClient",
    
    # Core models
    "Capability",
    "AIModel", 
    "ModelProvider",
    "ModelRegistry",
    
    # Exceptions
    "ClientError",
    "ConfigError", 
    "AuthError"
]