"""
Cache module for flow_api.

This module provides local file-based caching for tokens and models.
"""

import json
import time
import tempfile
from typing import Optional, Dict, Any
from pathlib import Path

from flow_api.utils.logging import get_logger


logger = get_logger(__name__)


class LocalCache:
    """Local file-based cache for tokens and models.
    
    This class provides caching functionality for authentication tokens
    and model lists to avoid unnecessary API calls.
    """
    
    _instance = None
    
    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super(LocalCache, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the cache manager."""
        if self._initialized:
            return
            
        # Use a cache directory in the user's temp directory
        self._cache_dir = Path(tempfile.gettempdir()) / "flow_api_cache"
        self._cache_dir.mkdir(exist_ok=True)
        
        self._token_cache_file = self._cache_dir / "token_cache.json"
        self._models_cache_file = self._cache_dir / "models_cache.json"
        
        self._initialized = True
        logger.debug(f"Cache initialized at: {self._cache_dir}")
    
    def _read_cache_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Read and parse a cache file.
        
        Args:
            file_path: Path to the cache file.
            
        Returns:
            The cached data if valid, None otherwise.
        """
        try:
            if not file_path.exists():
                return None
                
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # Check if cache has expired
            if 'expires_at' in data and time.time() > data['expires_at']:
                logger.debug(f"Cache expired: {file_path}")
                return None
                
            return data
            
        except (IOError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to read cache file {file_path}: {e}")
            return None
    
    def _write_cache_file(self, file_path: Path, data: Dict[str, Any]) -> None:
        """Write data to a cache file.
        
        Args:
            file_path: Path to the cache file.
            data: Data to cache.
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
            logger.debug(f"Cache written: {file_path}")
            
        except IOError as e:
            logger.warning(f"Failed to write cache file {file_path}: {e}")
    
    def get_cached_token(self) -> Optional[str]:
        """Get a cached authentication token if valid.
        
        Returns:
            The cached token if valid, None otherwise.
        """
        cache_data = self._read_cache_file(self._token_cache_file)
        if cache_data and 'access_token' in cache_data:
            return cache_data['access_token']
        return None
    
    def cache_token(self, token: str, expires_in: int) -> None:
        """Cache an authentication token.
        
        Args:
            token: The authentication token.
            expires_in: Token lifetime in seconds.
        """
        cache_data = {
            'access_token': token,
            'cached_at': time.time(),
            'expires_at': time.time() + expires_in
        }
        self._write_cache_file(self._token_cache_file, cache_data)
        logger.info("Authentication token cached")
    
    def get_cached_models(self) -> Optional[Dict[str, Any]]:
        """Get cached models if valid.
        
        Returns:
            The cached models if valid, None otherwise.
        """
        cache_data = self._read_cache_file(self._models_cache_file)
        if cache_data and 'models' in cache_data:
            return cache_data['models']
        return None
    
    def cache_models(self, models: Dict[str, Any], expires_in: int) -> None:
        """Cache models data.

        Args:
            models: API response containing models data.
            expires_in: Cache lifetime in seconds (default: 1 hour).
        """
        cache_data = {
            'models': models,
            'cached_at': time.time(),
            'expires_at': time.time() + expires_in
        }
        self._write_cache_file(self._models_cache_file, cache_data)
        # Count models from supportedModels if available
        model_count = 0
        if isinstance(models, dict) and 'supportedModels' in models:
            for provider_models in models['supportedModels'].values():
                if isinstance(provider_models, list):
                    model_count += len(provider_models)
        logger.debug(f"Models cached ({model_count} models)")
    
    def clear_cache(self) -> None:
        """Clear all cached data."""
        try:
            if self._token_cache_file.exists():
                self._token_cache_file.unlink()
            if self._models_cache_file.exists():
                self._models_cache_file.unlink()
            logger.info("Cache cleared")
        except OSError as e:
            logger.warning(f"Failed to clear cache: {e}")
    
    def clear_token_cache(self) -> None:
        """Clear only the token cache."""
        try:
            if self._token_cache_file.exists():
                self._token_cache_file.unlink()
            logger.info("Token cache cleared")
        except OSError as e:
            logger.warning(f"Failed to clear token cache: {e}")
    
    def clear_models_cache(self) -> None:
        """Clear only the models cache."""
        try:
            if self._models_cache_file.exists():
                self._models_cache_file.unlink()
            logger.info("Models cache cleared")
        except OSError as e:
            logger.warning(f"Failed to clear models cache: {e}")
