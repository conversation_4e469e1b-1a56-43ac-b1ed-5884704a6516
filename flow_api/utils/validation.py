"""
Validation utilities for flow_api.

This module provides validation utilities for the library.
"""

from typing import List, Dict, Any, Union, Optional
from flow_api.utils.logging import get_logger, log_and_raise


logger = get_logger(__name__)


def validate_messages(
    user_prompt: Optional[Union[str, List[Dict[str, Any]]]]
) -> List[Dict[str, Any]]:
    """Validate and normalize user messages.
    
    Args:
        user_prompt: The user prompt, either as a string or a list of message objects.
        
    Returns:
        A list of validated and normalized message objects.
        
    Raises:
        ValueError: If the messages are invalid.
    """
    if user_prompt is None:
        return []
    
    messages = []
    
    # Handle string input
    if isinstance(user_prompt, str):
        messages.append({
            "role": "user",
            "content": user_prompt
        })
        return messages
    
    # Handle list input
    if isinstance(user_prompt, list):
        for message in user_prompt:
            if not isinstance(message, dict):
                log_and_raise(logger, f"Expected message to be a dictionary, got {type(message)}", ValueError, "debug")

            # If message already has 'role', pass it through (it's already in OpenAI format)
            if "role" in message:
                if "content" not in message:
                    log_and_raise(logger, f"Message is missing 'content' field: {message}", ValueError, "debug")
                messages.append(message)
                continue

            # Handle legacy format with 'type' field
            if "type" not in message:
                # Default to text if no type is specified
                message_type = "text"
            else:
                message_type = message["type"]

            if "content" not in message:
                log_and_raise(logger, f"Message is missing 'content' field: {message}", ValueError, "debug")

            content = message["content"]

            # Convert legacy format to OpenAI format
            if message_type == "text":
                if not isinstance(content, str):
                    log_and_raise(logger, f"Text message content must be a string, got {type(content)}", ValueError, "debug")

                messages.append({
                    "role": "user",
                    "content": content
                })

            elif message_type == "image":
                # For image messages, we need to create OpenAI-compatible format
                if isinstance(content, str):
                    # Assume it's a base64 image
                    messages.append({
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{content}"
                                }
                            }
                        ]
                    })
                elif isinstance(content, dict):
                    if "data" not in content:
                        log_and_raise(logger, "Image message content dictionary is missing 'data' field", ValueError, "debug")

                    media_type = content.get("media_type", "image/jpeg")
                    messages.append({
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{media_type};base64,{content['data']}"
                                }
                            }
                        ]
                    })
                else:
                    log_and_raise(logger, f"Image message content must be a string or dictionary, got {type(content)}", ValueError, "debug")
            else:
                log_and_raise(logger, f"Unsupported message type: {message_type}", ValueError, "debug")

        return messages
    
    # Handle invalid input
    log_and_raise(logger, f"Expected user_prompt to be a string or list, got {type(user_prompt)}", ValueError, "debug")