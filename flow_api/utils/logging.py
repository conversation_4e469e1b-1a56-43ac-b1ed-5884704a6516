"""
Logging utilities for flow_api.

This module provides logging utilities for the library.
"""

import logging
import os
from typing import No<PERSON><PERSON>urn


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name.

    The logger behavior can be configured via environment variables:
    - FLOW_API_CONSOLE_LOG_LEVEL: Console log level (DEBUG, INFO, WARNING, ERROR, CRITICAL, OFF)
    - FLOW_API_FILE_LOG_LEVEL: File log level (DEBUG, INFO, WARNING, ERROR, CRITICAL, OFF)
    - FLOW_API_LOG_FILE_PATH: Path to log file (if not set, no file logging)

    Args:
        name: The name of the logger.

    Returns:
        A logger instance.
    """
    logger = logging.getLogger(name)

    # Set logger to capture all levels (DEBUG and above)
    # Individual handlers will filter what they actually output
    logger.setLevel(logging.DEBUG)

    # Add handlers if none exist
    if not logger.handlers:
        # Get configuration from centralized config
        from flow_api.config import Config
        config = Config()

        # Console handler - configurable level
        console_level = config.console_log_level

        # Only add console handler if not disabled
        if console_level <= logging.CRITICAL:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(console_level)
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)

        # File handler - only if log file path is specified in configuration
        log_file_path = config.log_file_path
        if log_file_path:
            try:
                # Create directory if it doesn't exist
                log_dir = os.path.dirname(log_file_path)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir, exist_ok=True)

                # File log level - configurable
                file_level = config.file_log_level

                # Only add file handler if not disabled
                if file_level <= logging.CRITICAL:
                    file_handler = logging.FileHandler(log_file_path)
                    file_handler.setLevel(file_level)
                    file_formatter = logging.Formatter(
                        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                    )
                    file_handler.setFormatter(file_formatter)
                    logger.addHandler(file_handler)
            except (OSError, IOError) as e:
                # If file logging fails, log the error to console (if console logging is enabled)
                if console_level <= logging.WARNING:
                    error_record = logging.LogRecord(
                        name=name,
                        level=logging.WARNING,
                        pathname="",
                        lineno=0,
                        msg=f"Failed to setup file logging: {e}",
                        args=(),
                        exc_info=None
                    )
                    # Find console handler and use it
                    for handler in logger.handlers:
                        if isinstance(handler, logging.StreamHandler):
                            handler.handle(error_record)
                            break

    return logger


def log_and_raise(logger: logging.Logger, message: str, exception_class=Exception, log_level: str = "debug") -> NoReturn:
    """Log a message and raise an exception.

    This utility function helps avoid duplicate messages by logging at an appropriate
    level (usually debug for user input errors) while still raising the exception
    with the full message for the user.

    Args:
        logger: The logger instance to use.
        message: The message to log and include in the exception.
        exception_class: The exception class to raise (default: Exception).
        log_level: The log level to use ("debug", "info", "warning", "error").
    """
    # Log at the specified level (won't appear in console if debug/info)
    getattr(logger, log_level.lower())(message)

    # Raise the exception with the full message for the user
    raise exception_class(message)


def extract_error_message(error_response: dict) -> str:
    """Extract the most detailed error message from an API error response.

    This function navigates through various common error response structures
    to find the most specific error message available.

    Args:
        error_response: The parsed JSON error response from the API.

    Returns:
        The most detailed error message found.
    """
    # Default message if nothing better is found
    message = error_response.get('message', '[No message content]')

    # Check for detailed error in history array first (most specific)
    if 'history' in error_response and isinstance(error_response['history'], list) and error_response['history']:
        # Get the first entry in history which usually contains the most specific error
        first_entry = error_response['history'][0]

        # Check for message in response object
        if 'response' in first_entry and isinstance(first_entry['response'], dict):
            response_obj = first_entry['response']
            if 'message' in response_obj:
                # Extract just the error part after the colon if it exists
                # e.g., "messages: Field required" -> "Field required"
                full_message = response_obj['message']
                if ':' in full_message:
                    message = full_message.split(':', 1)[1].strip()
                else:
                    message = full_message
                return message

        # Check for direct message in history entry
        if 'message' in first_entry:
            full_message = first_entry['message']
            if ':' in full_message:
                message = full_message.split(':', 1)[1].strip()
            else:
                message = full_message
            return message

    # Check if error is directly in the response as a string
    if 'error' in error_response and isinstance(error_response['error'], str):
        message = error_response['error']

    # Check if error is an object with a message
    elif 'error' in error_response and isinstance(error_response['error'], dict):
        error_obj = error_response['error']
        if 'message' in error_obj:
            message = error_obj['message']

    return message
