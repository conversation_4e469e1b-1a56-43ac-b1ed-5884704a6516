"""
Parameter validation module for flow_api.

This module provides validation for model-specific parameters.
"""

from typing import Dict, Any
from flow_api.core.models import AIModel
from flow_api.core.capabilities import Capability
from flow_api.exceptions.client_error import ClientError


class ParameterValidator:
    """Validator for model-specific parameters."""
    
    # Parameter constraints by provider and model type
    PARAMETER_CONSTRAINTS = {
        "azure-openai": {
            "chat": {
                "temperature": {"type": float, "min": 0.0, "max": 2.0},
                "max_tokens": {"type": int, "min": 1, "max": 128000},
                "top_p": {"type": float, "min": 0.0, "max": 1.0},
                "frequency_penalty": {"type": float, "min": -2.0, "max": 2.0},
                "presence_penalty": {"type": float, "min": -2.0, "max": 2.0},
                "stream": {"type": bool},
                "seed": {"type": int, "min": -9223372036854775808, "max": 9223372036854775807},
                "reasoning_effort": {"type": str, "values": ["low", "medium", "high"]}
            },
            "embedding": {
                "encoding_format": {"type": str, "values": ["float", "base64"]},
                "dimensions": {"type": int, "min": 1, "max": 3072},
                "user": {"type": str, "max_length": 256}
            },
            "image_generation": {
                "n": {"type": int, "min": 1, "max": 10},
                "size": {"type": str, "values": ["256x256", "512x512", "1024x1024", "1792x1024", "1024x1792"]},
                "quality": {"type": str, "values": ["standard", "hd"]},
                "style": {"type": str, "values": ["vivid", "natural"]},
                "response_format": {"type": str, "values": ["url", "b64_json"]},
                "user": {"type": str, "max_length": 256}
            }
        },
        "azure-foundry": {
            "chat": {
                "temperature": {"type": float, "min": 0.0, "max": 2.0},
                "max_tokens": {"type": int, "min": 1, "max": 8192},
                "top_p": {"type": float, "min": 0.0, "max": 1.0},
                "stream": {"type": bool}
            }
        },
        "google-gemini": {
            "chat": {
                "temperature": {"type": float, "min": 0.0, "max": 2.0},
                "maxOutputTokens": {"type": int, "min": 1, "max": 8192},
                "topP": {"type": float, "min": 0.0, "max": 1.0},
                "topK": {"type": int, "min": 1, "max": 40}
            },
            "embedding": {}
        },
        "amazon-bedrock": {
            "chat": {
                "temperature": {"type": float, "min": 0.0, "max": 1.0},
                "max_tokens": {"type": int, "min": 1, "max": 4096},
                "top_p": {"type": float, "min": 0.0, "max": 1.0},
                "top_k": {"type": int, "min": 0, "max": 500},
                "stop_sequences": {"type": list, "max_length": 4}
            },
            "embedding": {}
        },
        "azure-ai-speech": {
            "speech_to_text": {
                "language": {"type": str, "max_length": 10},
                "prompt": {"type": str, "max_length": 244},
                "response_format": {"type": str, "values": ["json", "text", "srt", "verbose_json", "vtt"]},
                "temperature": {"type": float, "min": 0.0, "max": 1.0},
                "timestamp_granularities": {"type": list, "values": ["word", "segment"]}
            }
        }
    }
    
    # Special constraints for specific models
    MODEL_SPECIFIC_CONSTRAINTS = {
        "o1": {
            # o1 models don't support certain parameters
            "unsupported": ["temperature", "top_p", "frequency_penalty", "presence_penalty", "stream"],
            "required": ["reasoning_effort"]
        },
        "o1-mini": {
            "unsupported": ["temperature", "top_p", "frequency_penalty", "presence_penalty", "stream"],
            "required": ["reasoning_effort"]
        },
        "o3-mini": {
            "unsupported": ["temperature", "top_p", "frequency_penalty", "presence_penalty", "stream"],
            "required": ["reasoning_effort"]
        }
    }
    
    @classmethod
    def validate_parameters(cls, model: AIModel, capability: Capability, **kwargs) -> None:
        """Validate parameters for a specific model and capability.
        
        Args:
            model: The AI model.
            capability: The capability being used.
            **kwargs: Parameters to validate.
            
        Raises:
            ClientError: If validation fails.
        """
        provider = model.provider.value
        capability_name = cls._get_capability_name(capability)
        
        # Get constraints for this provider and capability
        provider_constraints = cls.PARAMETER_CONSTRAINTS.get(provider, {})
        capability_constraints = provider_constraints.get(capability_name, {})
        
        # Check model-specific constraints
        model_constraints = cls._get_model_constraints(model.name)
        
        # Validate each parameter
        for param_name, param_value in kwargs.items():
            cls._validate_parameter(
                param_name, param_value, capability_constraints, 
                model_constraints, model.name
            )
    
    @classmethod
    def _get_capability_name(cls, capability: Capability) -> str:
        """Map capability enum to constraint key."""
        mapping = {
            Capability.CHAT_CONVERSATION: "chat",
            Capability.TEXT_EMBEDDING: "embedding",
            Capability.IMAGE_GENERATION: "image_generation",
            Capability.SPEECH_TO_TEXT: "speech_to_text",
            Capability.IMAGE_RECOGNITION: "chat"  # Uses chat format
        }
        return mapping.get(capability, "unknown")
    
    @classmethod
    def _get_model_constraints(cls, model_name: str) -> Dict[str, Any]:
        """Get model-specific constraints."""
        for model_pattern, constraints in cls.MODEL_SPECIFIC_CONSTRAINTS.items():
            if model_name.startswith(model_pattern):
                return constraints
        return {}
    
    @classmethod
    def _validate_parameter(cls, param_name: str, param_value: Any, 
                          capability_constraints: Dict[str, Any],
                          model_constraints: Dict[str, Any],
                          model_name: str) -> None:
        """Validate a single parameter."""
        # Check if parameter is unsupported for this model
        unsupported = model_constraints.get("unsupported", [])
        if param_name in unsupported:
            raise ClientError(
                f"Parameter '{param_name}' is not supported for model '{model_name}'"
            )
        
        # Get parameter constraints
        param_constraints = capability_constraints.get(param_name)
        if not param_constraints:
            # Parameter not defined in constraints, allow it
            return
        
        # Validate type
        expected_type = param_constraints.get("type")
        if expected_type and not isinstance(param_value, expected_type):
            raise ClientError(
                f"Parameter '{param_name}' must be of type {expected_type.__name__}, "
                f"got {type(param_value).__name__}"
            )
        
        # Validate numeric ranges
        if isinstance(param_value, (int, float)):
            min_val = param_constraints.get("min")
            max_val = param_constraints.get("max")
            
            if min_val is not None and param_value < min_val:
                raise ClientError(
                    f"Parameter '{param_name}' must be >= {min_val}, got {param_value}"
                )
            
            if max_val is not None and param_value > max_val:
                raise ClientError(
                    f"Parameter '{param_name}' must be <= {max_val}, got {param_value}"
                )
        
        # Validate string length
        if isinstance(param_value, str):
            max_length = param_constraints.get("max_length")
            if max_length is not None and len(param_value) > max_length:
                raise ClientError(
                    f"Parameter '{param_name}' must be <= {max_length} characters, "
                    f"got {len(param_value)}"
                )
        
        # Validate allowed values
        allowed_values = param_constraints.get("values")
        if allowed_values is not None:
            if isinstance(param_value, list):
                # For list parameters, validate each item
                for item in param_value:
                    if item not in allowed_values:
                        raise ClientError(
                            f"Parameter '{param_name}' contains invalid value '{item}'. "
                            f"Allowed values: {allowed_values}"
                        )
            else:
                if param_value not in allowed_values:
                    raise ClientError(
                        f"Parameter '{param_name}' must be one of {allowed_values}, "
                        f"got '{param_value}'"
                    )
        
        # Validate list constraints
        if isinstance(param_value, list):
            max_length = param_constraints.get("max_length")
            if max_length is not None and len(param_value) > max_length:
                raise ClientError(
                    f"Parameter '{param_name}' list must have <= {max_length} items, "
                    f"got {len(param_value)}"
                )
    
    @classmethod
    def check_required_parameters(cls, model: AIModel, capability: Capability, **kwargs) -> None:
        """Check if all required parameters are provided.
        
        Args:
            model: The AI model.
            capability: The capability being used.
            **kwargs: Provided parameters.
            
        Raises:
            ClientError: If required parameters are missing.
        """
        model_constraints = cls._get_model_constraints(model.name)
        required_params = model_constraints.get("required", [])
        
        for param in required_params:
            if param not in kwargs:
                raise ClientError(
                    f"Required parameter '{param}' is missing for model '{model.name}'"
                )
