"""
FlowAPIClient module for flow_api.

This module provides the main client interface for the library.
"""

from typing import List, Dict, Any, Optional, Union

from flow_api.core.capabilities import Capability
from flow_api.core.model_registry import ModelRegistry
from flow_api.adapters.inbound.model_factory import ModelFactory
from flow_api.adapters.inbound.capability_factory import CapabilityClientManager
from flow_api.adapters.outbound.api.specialized_client import SpecializedClient
from flow_api.exceptions.client_error import ClientError
from flow_api.utils.validation import validate_messages
from flow_api.utils.logging import get_logger, log_and_raise


logger = get_logger(__name__)


class FlowAPIClient:
    """Main client interface for flow_api.

    This class provides a high-level interface for interacting with AI models.
    """

    def __init__(self, default_model: Optional[str] = None):
        """Initialize the client.

        Args:
            default_model: The default model to use if none is specified.
        """
        logger.debug("=========== Starting FlowAPIClient  ===========")
        self._model_registry = ModelRegistry()
        self._current_manager: Optional[CapabilityClientManager] = None
        self._current_client: Optional[SpecializedClient] = None

        # Set the default model if provided
        if default_model:
            self.with_model(default_model)
    
    def with_model(self, model_name: str) -> 'FlowAPIClient':
        """Set the model to use for subsequent API calls.

        Args:
            model_name: The name of the model.

        Returns:
            The client instance for chaining.

        Raises:
            ClientError: If the model does not exist.
        """
        self._current_manager = ModelFactory.create_client_manager(model_name)
        self._current_client = None  # Will be set when a capability is needed
        return self
    
    def with_capability(self, capability: Union[str, Capability]) -> 'FlowAPIClient':
        """Set the model to use based on a required capability.

        If multiple models have the capability, the first one is used.

        Args:
            capability: The required capability.

        Returns:
            The client instance for chaining.

        Raises:
            ClientError: If no model with the capability exists.
        """
        # Convert string to Capability enum if needed
        capability_enum: Capability
        if isinstance(capability, str):
            try:
                capability_enum = Capability.from_str(capability)
            except ValueError:
                log_and_raise(logger, f"Invalid capability: {capability}", ClientError, "debug")
        else:
            capability_enum = capability

        # Get models with the capability
        models = self._model_registry.get_models_by_capability(capability_enum)

        if not models:
            log_and_raise(logger, f"No model found with capability '{capability_enum.value}'", ClientError, "debug")

        # Use the first model with the capability
        model = models[0]
        self._current_manager = ModelFactory.create_client_manager_for_model(model)
        self._current_client = self._current_manager.get_client(capability_enum)

        return self
    
    def with_capabilities(self, capabilities: List[Union[str, Capability]]) -> 'FlowAPIClient':
        """Set the model to use based on required capabilities.

        If multiple models have all the capabilities, the first one is used.

        Args:
            capabilities: The required capabilities.

        Returns:
            The client instance for chaining.

        Raises:
            ClientError: If no model with all the capabilities exists.
        """
        # Convert strings to Capability enums if needed
        capability_enums = []
        for cap in capabilities:
            if isinstance(cap, str):
                try:
                    capability_enums.append(Capability.from_str(cap))
                except ValueError:
                    log_and_raise(logger, f"Invalid capability: {cap}", ClientError, "debug")
            else:
                capability_enums.append(cap)

        # Get models with all the capabilities
        models = self._model_registry.get_models_by_capabilities(capability_enums)

        if not models:
            cap_values = [cap.value for cap in capability_enums]
            log_and_raise(logger, f"No model found with all capabilities: {cap_values}", ClientError, "debug")

        # Use the first model with all the capabilities
        model = models[0]
        self._current_manager = ModelFactory.create_client_manager_for_model(model)
        # Use the first capability to get a client (user can switch later if needed)
        self._current_client = self._current_manager.get_client(capability_enums[0])

        return self
    
    def get_current_model(self) -> Optional[str]:
        """Get the name of the currently selected model.

        Returns:
            The name of the currently selected model, or None if no model is selected.
        """
        if self._current_manager:
            return self._current_manager.model.name
        return None
    
    def list_models(self) -> List[Dict[str, Any]]:
        """List all available models.
        
        Returns:
            A list of all available models as dictionaries.
        """
        return [model.to_dict() for model in self._model_registry.get_all_models()]
    
    def list_capabilities(self) -> List[str]:
        """List all available capabilities.

        Returns:
            A list of all available capabilities as strings.
        """
        return [cap.value for cap in self._model_registry.get_available_capabilities()]

    def check_connection(self) -> Dict[str, Any]:
        """Check the connection to the API and ensure authentication is working.

        This method allows you to verify the connection before making API calls,
        avoiding delays during actual requests. It will obtain a new authentication
        token if needed and cache it for subsequent use.

        Returns:
            A dictionary containing connection status information:
            - 'status': 'connected' if successful, 'error' if failed
            - 'token_source': 'cache' if using cached token, 'new' if obtained new token
            - 'message': Human-readable status message
            - 'error': Error message if status is 'error' (optional)

        Example:
            >>> client = FlowAPIClient()
            >>> result = client.check_connection()
            >>> if result['status'] == 'connected':
            ...     print(f"✅ {result['message']}")
            ... else:
            ...     print(f"❌ {result['message']}: {result.get('error', '')}")
        """
        from flow_api.adapters.outbound.auth.token_manager import TokenManager

        logger.info("Checking API connection...")
        token_manager = TokenManager()
        result = token_manager.check_connection()

        # Log the result for user visibility
        if result['status'] == 'connected':
            logger.info(f"✅ Connection check successful: {result['message']}")
        else:
            logger.error(f"❌ Connection check failed: {result['message']}")
            if 'error' in result:
                logger.error(f"Error details: {result['error']}")

        return result
    
    def send(
        self,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        **kwargs
    ) -> Union[Dict[str, Any], Any]:
        """Send a request to the AI model.

        Args:
            system_prompt: The system prompt.
            user_prompt: The user prompt, either as a string or a list of message objects.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response.

        Raises:
            ClientError: If no model is selected or the request fails.
        """
        # Ensure we have a model selected
        if self._current_manager is None:
            # Try to use a default model if none is selected
            models = self._model_registry.get_all_models()
            if models:
                self._current_manager = ModelFactory.create_client_manager_for_model(models[0])
                # Use chat capability as default
                from flow_api.core.capabilities import Capability
                self._current_client = self._current_manager.get_client(Capability.CHAT_CONVERSATION)
            else:
                log_and_raise(logger, "No model selected. Use with_model() or with_capability() first.", ClientError, "debug")

        # If no specific client is set, use chat as default
        if self._current_client is None and self._current_manager is not None:
            from flow_api.core.capabilities import Capability
            self._current_client = self._current_manager.get_client(Capability.CHAT_CONVERSATION)

        # Process user_prompt to ensure it's in the correct format
        messages = validate_messages(user_prompt)

        # Build the request using the specialized client
        return self._send_with_specialized_client(system_prompt, messages, **kwargs)

    def _send_with_specialized_client(
        self,
        system_prompt: Optional[str],
        messages: List[Dict[str, Any]],
        **kwargs
    ) -> Dict[str, Any]:
        """Send a request using the specialized client.

        Args:
            system_prompt: The system prompt.
            messages: The user messages.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response.

        Raises:
            ClientError: If the request fails.
        """
        import json
        import requests
        from flow_api.adapters.outbound.auth.token_manager import TokenManager
        from flow_api.config import Config

        # Ensure we have a current client
        if self._current_client is None:
            log_and_raise(logger, "No specialized client available. Use with_model() or with_capability() first.", ClientError, "debug")

        # Prepare messages for the API
        api_messages = []
        if system_prompt:
            api_messages.append({"role": "system", "content": system_prompt})
        api_messages.extend(messages)

        # Build the request payload using the specialized client
        # For speech clients, don't pass messages
        if hasattr(self._current_client, 'requires_multipart') and self._current_client.requires_multipart():
            payload = self._current_client.build_request_payload(**kwargs)
        else:
            payload = self._current_client.build_request_payload(
                messages=api_messages,
                **kwargs
            )

        # Log payload (skip for multipart to avoid binary data issues)
        if hasattr(self._current_client, 'requires_multipart') and self._current_client.requires_multipart():
            logger.debug(f"Multipart payload prepared with keys: {list(payload.keys())}")
        else:
            logger.debug(f"Payload before sending: {json.dumps(payload, indent=2)}")

        # Get authentication token
        token_manager = TokenManager()
        token = token_manager.get_valid_token()

        # Get endpoint URL
        config = Config()
        endpoint_path = self._current_client.get_endpoint_path()
        url = f"{config.api_base_url}{endpoint_path}"

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': config.flow_tenant,
            'Authorization': f'Bearer {token}',
            'FlowAgent': self._current_client.model.name
        }

        # Make the API request
        try:
            logger.debug(f"Sending request to {url}")

            # Check if client requires multipart/form-data
            if hasattr(self._current_client, 'requires_multipart') and self._current_client.requires_multipart():
                # Remove Content-Type header for multipart (requests will set it automatically)
                if 'Content-Type' in headers:
                    del headers['Content-Type']

                # Separate files from regular form data
                files = {}
                data = {}
                for key, value in payload.items():
                    if isinstance(value, tuple) and len(value) == 3:
                        # This is a file (filename, content, content_type)
                        files[key] = value
                    else:
                        # This is regular form data
                        data[key] = value

                # Send multipart request
                response = requests.post(url, headers=headers, files=files, data=data)
                response.raise_for_status()
                result = response.json()
            else:
                # Regular JSON request
                response = requests.post(url, headers=headers, data=json.dumps(payload))
                response.raise_for_status()
                result = response.json()

            # Process the response based on provider format
            if 'candidates' in result:
                # Gemini format - convert to OpenAI format
                choices = []
                for candidate in result.get('candidates', []):
                    content_obj = candidate.get('content', {})
                    parts = content_obj.get('parts', [])

                    # Combine all text parts
                    content = ""
                    for part in parts:
                        if 'text' in part:
                            content += part['text']

                    choice = {
                        "message": {
                            "role": "assistant",
                            "content": content
                        },
                        "finish_reason": candidate.get('finishReason', 'stop').lower()
                    }
                    choices.append(choice)

                return {
                    "choices": choices,
                    "model": result.get("modelVersion", self._current_client.model.name),
                    "usage": self._convert_gemini_usage(result.get("usageMetadata", {}))
                }
            elif 'generation' in result or 'content' in result or 'output' in result:
                # Bedrock format - convert to OpenAI format
                content = ""
                stop_reason = "stop"

                if 'output' in result:
                    # Nova format: output.message.content array
                    output = result.get('output', {})
                    message = output.get('message', {})
                    content_data = message.get('content', [])

                    if isinstance(content_data, list):
                        for item in content_data:
                            if isinstance(item, dict) and 'text' in item:
                                content += item.get('text', '')

                    stop_reason = result.get('stopReason', 'stop')

                elif 'content' in result:
                    # Claude format: content is array of objects
                    content_data = result.get('content', [])
                    if isinstance(content_data, list):
                        for item in content_data:
                            if isinstance(item, dict) and item.get('type') == 'text':
                                content += item.get('text', '')
                    elif isinstance(content_data, dict):
                        content = content_data.get('text', '')

                    stop_reason = result.get('stop_reason', 'stop')

                elif 'generation' in result:
                    # Other Bedrock models format
                    content = result.get('generation', '')
                    stop_reason = result.get('stop_reason', 'stop')

                choice = {
                    "message": {
                        "role": "assistant",
                        "content": content
                    },
                    "finish_reason": stop_reason
                }

                return {
                    "choices": [choice],
                    "model": result.get("model", self._current_client.model.name),
                    "usage": self._convert_bedrock_usage(result)
                }
            elif 'data' in result:
                # Image generation format - return as-is
                return result
            elif 'combinedPhrases' in result or 'phrases' in result:
                # Speech-to-text format - return as-is
                return result
            else:
                # OpenAI format
                choices = result.get('choices', [])
                if not choices:
                    log_and_raise(logger, f"API response does not contain 'choices': {result}", ClientError, "warning")

                return {
                    "choices": choices,
                    "model": result.get("model", self._current_client.model.name),
                    "usage": result.get("usage", {})
                }

        except requests.exceptions.HTTPError as err:
            log_and_raise(logger, f"HTTP error: {err}", ClientError, "error")
        except requests.exceptions.RequestException as err:
            log_and_raise(logger, f"Request error: {err}", ClientError, "error")
        except Exception as err:
            log_and_raise(logger, f"Unexpected error: {err}", ClientError, "error")

    def _handle_streaming_response(self, response):
        """Handle streaming response and return an iterator of chunks.

        Args:
            response: The streaming HTTP response.

        Yields:
            Parsed chunks from the streaming response.
        """
        import json

        for line in response.iter_lines(decode_unicode=True):
            if line:
                # Skip empty lines and handle SSE format
                line_str = line.decode('utf-8') if isinstance(line, bytes) else line
                if line_str.startswith('data: '):
                    data_content = line_str[6:]  # Remove 'data: ' prefix

                    # Check for end of stream
                    if data_content.strip() == '[DONE]':
                        break

                    try:
                        # Parse the JSON chunk
                        chunk = json.loads(data_content)

                        # Extract content from the chunk
                        if 'choices' in chunk and chunk['choices']:
                            choice = chunk['choices'][0]
                            if 'delta' in choice and 'content' in choice['delta']:
                                content = choice['delta']['content']
                                if content:  # Only yield non-empty content
                                    yield content
                    except json.JSONDecodeError:
                        # Skip malformed JSON chunks
                        continue

    def _convert_gemini_usage(self, usage_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Gemini usage metadata to OpenAI format.

        Args:
            usage_metadata: Gemini usage metadata.

        Returns:
            Usage data in OpenAI format.
        """
        return {
            "prompt_tokens": usage_metadata.get("promptTokenCount", 0),
            "completion_tokens": usage_metadata.get("candidatesTokenCount", 0),
            "total_tokens": usage_metadata.get("totalTokenCount", 0)
        }

    def _convert_bedrock_usage(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Bedrock usage data to OpenAI format.

        Args:
            result: Bedrock API response.

        Returns:
            Usage data in OpenAI format.
        """
        prompt_tokens = 0
        completion_tokens = 0

        if 'usage' in result:
            usage = result['usage']

            # Nova format
            if 'inputTokens' in usage:
                prompt_tokens = usage.get("inputTokens", 0)
                completion_tokens = usage.get("outputTokens", 0)
            # Claude format
            elif 'input_tokens' in usage:
                prompt_tokens = usage.get("input_tokens", 0)
                completion_tokens = usage.get("output_tokens", 0)
        else:
            # Other Bedrock models format
            prompt_tokens = result.get("prompt_token_count", 0)
            completion_tokens = result.get("generation_token_count", 0)

        return {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": prompt_tokens + completion_tokens
        }

    def get_answer(
        self,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        **kwargs
    ) -> str:
        """Get the answer from the AI model.

        This is a convenience method that extracts the answer content from the API response.

        Args:
            system_prompt: The system prompt.
            user_prompt: The user prompt, either as a string or a list of message objects.
            **kwargs: Additional parameters for the API request.

        Returns:
            The answer content from the API response.

        Raises:
            ClientError: If no model is selected or the request fails.
        """
        # Get the full response
        response = self.send(system_prompt, user_prompt, **kwargs)

        # Extract content from the response
        content = ""
        choices = response.get("choices", [])

        if not choices:
            log_and_raise(logger, "No choices found in API response", ClientError, "warning")

        for i, choice in enumerate(choices):
            message = choice.get("message", {})
            part_content = message.get("content", "")
            if not part_content:
                logger.warning(f"Choice {i} contains empty or invalid 'content': {message}")
                # Don't raise error immediately, continue with other choices
                continue
            content += part_content

        if not content:
            log_and_raise(logger, "API response contains no valid content", ClientError, "warning")

        return content

    def get_stream_answer(
        self,
        system_prompt: Optional[str] = None,
        user_prompt: Optional[Union[str, List[Dict[str, Any]]]] = None,
        **kwargs
    ):
        """Get a streaming answer from the AI model.

        Args:
            system_prompt: The system prompt.
            user_prompt: The user prompt, either as a string or a list of message objects.
            **kwargs: Additional parameters for the API request.

        Yields:
            Content chunks from the streaming response.

        Raises:
            ClientError: If no model is selected or the request fails.
        """
        import json
        import requests
        from flow_api.adapters.outbound.auth.token_manager import TokenManager
        from flow_api.adapters.inbound.model_factory import ModelFactory
        from flow_api.config import Config

        # Ensure we have a model selected
        if self._current_manager is None:
            # Try to use a default model if none is selected
            models = self._model_registry.get_all_models()
            if models:
                self._current_manager = ModelFactory.create_client_manager_for_model(models[0])
                # Use chat capability as default
                from flow_api.core.capabilities import Capability
                self._current_client = self._current_manager.get_client(Capability.CHAT_CONVERSATION)
            else:
                log_and_raise(logger, "No model selected. Use with_model() or with_capability() first.", ClientError, "debug")

        # If no specific client is set, use chat as default
        if self._current_client is None and self._current_manager is not None:
            from flow_api.core.capabilities import Capability
            self._current_client = self._current_manager.get_client(Capability.CHAT_CONVERSATION)

        # Final check to ensure we have a client
        if self._current_client is None:
            log_and_raise(logger, "No specialized client available. Use with_model() or with_capability() first.", ClientError, "debug")

        # Process user_prompt to ensure it's in the correct format
        messages = validate_messages(user_prompt)

        # Prepare messages for the API
        api_messages = []
        if system_prompt:
            api_messages.append({"role": "system", "content": system_prompt})
        api_messages.extend(messages)

        # Force streaming mode
        kwargs['stream'] = True

        # Build the request payload using the specialized client
        payload = self._current_client.build_request_payload(
            messages=api_messages,
            **kwargs
        )
        logger.debug(f"Streaming payload: {json.dumps(payload, indent=2)}")

        # Get authentication token
        token_manager = TokenManager()
        token = token_manager.get_valid_token()

        # Get endpoint URL
        config = Config()
        endpoint_path = self._current_client.get_endpoint_path()
        url = f"{config.api_base_url}{endpoint_path}"

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': config.flow_tenant,
            'Authorization': f'Bearer {token}',
            'FlowAgent': self._current_client.model.name
        }

        # Make the streaming API request
        try:
            logger.debug(f"Sending streaming request to {url}")
            response = requests.post(url, headers=headers, data=json.dumps(payload), stream=True)
            response.raise_for_status()

            # Process streaming response
            for chunk in self._handle_streaming_response(response):
                yield chunk

        except requests.exceptions.HTTPError as err:
            log_and_raise(logger, f"HTTP error: {err}", ClientError, "error")
        except requests.exceptions.RequestException as err:
            log_and_raise(logger, f"Request error: {err}", ClientError, "error")
        except Exception as err:
            log_and_raise(logger, f"Unexpected error: {err}", ClientError, "error")

    def generate_embedding(
        self,
        text: str,
        model: Optional[str] = None,
        encoding_format: str = "float",
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text embeddings.

        Args:
            text: The text to embed.
            model: The model to use. If None, uses current model or finds one with text-embedding capability.
            encoding_format: The encoding format for the embeddings.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response containing embeddings.

        Raises:
            ClientError: If no suitable model is found or the request fails.
        """
        # Set model if provided
        if model:
            self.with_model(model)
        elif self._current_manager is None:
            # Auto-select a model with text embedding capability
            from flow_api.core.capabilities import Capability
            self.with_capability(Capability.TEXT_EMBEDDING)

        # Ensure we have a manager
        if self._current_manager is None:
            log_and_raise(logger, "No model manager available for text embedding", ClientError, "debug")

        # Set the text embedding capability
        from flow_api.core.capabilities import Capability
        self._current_client = self._current_manager.get_client(Capability.TEXT_EMBEDDING)

        return self.send(input=text, encoding_format=encoding_format, **kwargs)

    def generate_image(
        self,
        prompt: str,
        model: Optional[str] = None,
        size: str = "1024x1024",
        quality: str = "standard",
        n: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate images from text prompts.

        Args:
            prompt: The text prompt for image generation.
            model: The model to use. If None, uses current model or finds one with image-generation capability.
            size: The size of the generated image.
            quality: The quality of the generated image.
            n: The number of images to generate.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response containing generated images.

        Raises:
            ClientError: If no suitable model is found or the request fails.
        """
        # Set model if provided
        if model:
            self.with_model(model)
        elif self._current_manager is None:
            # Auto-select a model with image generation capability
            from flow_api.core.capabilities import Capability
            self.with_capability(Capability.IMAGE_GENERATION)

        # Ensure we have a manager
        if self._current_manager is None:
            log_and_raise(logger, "No model manager available for image generation", ClientError, "debug")

        # Set the image generation capability
        from flow_api.core.capabilities import Capability
        self._current_client = self._current_manager.get_client(Capability.IMAGE_GENERATION)

        return self.send(prompt=prompt, size=size, quality=quality, n=n, **kwargs)

    def generate_transcript(
        self,
        audio_data: str,
        model: Optional[str] = None,
        language: str = "en",
        response_format: str = "json",
        temperature: float = 0.0,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate transcript from speech (speech-to-text)."

        Args:
            audio_data: The base64-encoded audio data.
            model: The model to use. If None, uses current model or finds one with speech-to-text capability.
            language: The language of the audio.
            response_format: The format of the response.
            temperature: The sampling temperature.
            **kwargs: Additional parameters for the API request.

        Returns:
            The API response containing transcription.

        Raises:
            ClientError: If no suitable model is found or the request fails.
        """
        # Set model if provided
        if model:
            self.with_model(model)
        elif self._current_manager is None:
            # Auto-select a model with speech-to-text capability
            from flow_api.core.capabilities import Capability
            self.with_capability(Capability.SPEECH_TO_TEXT)

        # Ensure we have a manager
        if self._current_manager is None:
            log_and_raise(logger, "No model manager available for speech-to-text", ClientError, "debug")

        # Set the speech-to-text capability
        from flow_api.core.capabilities import Capability
        self._current_client = self._current_manager.get_client(Capability.SPEECH_TO_TEXT)

        return self.send(file=audio_data, language=language, response_format=response_format, temperature=temperature, **kwargs)