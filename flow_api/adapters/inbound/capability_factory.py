"""
Capability factory module for flow_api.

This module provides a factory for creating specialized clients based on capabilities.
"""

from typing import Type, Dict

from flow_api.core.models import AIModel
from flow_api.core.capabilities import Capability
from flow_api.adapters.outbound.api.specialized_client import (
    SpecializedClient,
    ChatClient,
    EmbeddingClient,
    ImageGenerationClient,
    SpeechClient
)
from flow_api.exceptions.client_error import ClientError


class CapabilityFactory:
    """Factory for creating specialized clients based on capabilities.
    
    This class maps capabilities to their respective specialized client classes.
    """
    
    # Mapping of capabilities to specialized client classes
    _capability_mapping: Dict[Capability, Type[SpecializedClient]] = {
        Capability.CHAT_CONVERSATION: ChatClient,
        Capability.TEXT_EMBEDDING: EmbeddingClient,
        Capability.IMAGE_GENERATION: ImageGenerationClient,
        Capability.SPEECH_TO_TEXT: SpeechClient,
        # Image recognition uses ChatClient with special message format
        Capability.IMAGE_RECOGNITION: ChatClient,
        # Streaming uses Chat<PERSON><PERSON> with stream parameter
        Capability.STREAMING: ChatClient,
    }
    
    @classmethod
    def create_client(cls, model: AIModel, capability: Capability) -> SpecializedClient:
        """Create a specialized client for a specific model and capability.
        
        Args:
            model: The AI model to use.
            capability: The capability required.
            
        Returns:
            A specialized client instance for the specified capability.
            
        Raises:
            ClientError: If the model doesn't support the capability or no client is available.
        """
        # Check if the model supports the capability
        if not model.has_capability(capability):
            raise ClientError(
                f"Model '{model.name}' does not support capability '{capability.value}'"
            )
        
        # Get the client class for the capability
        client_class = cls._capability_mapping.get(capability)
        
        if client_class is None:
            raise ClientError(f"No client available for capability '{capability.value}'")
        
        # Create and return the client instance
        return client_class(model)
    
    @classmethod
    def get_supported_capabilities(cls) -> list[Capability]:
        """Get all supported capabilities.
        
        Returns:
            A list of all supported capabilities.
        """
        return list(cls._capability_mapping.keys())
    
    @classmethod
    def is_capability_supported(cls, capability: Capability) -> bool:
        """Check if a capability is supported.
        
        Args:
            capability: The capability to check.
            
        Returns:
            True if the capability is supported, False otherwise.
        """
        return capability in cls._capability_mapping


class CapabilityClientManager:
    """Manager for handling multiple capability clients for a model.
    
    This class provides a convenient interface for working with multiple
    capabilities of a single model.
    """
    
    def __init__(self, model: AIModel):
        """Initialize the capability client manager.
        
        Args:
            model: The AI model to manage clients for.
        """
        self.model = model
        self._clients: Dict[Capability, SpecializedClient] = {}
    
    def get_client(self, capability: Capability) -> SpecializedClient:
        """Get a client for a specific capability.

        Args:
            capability: The capability required.

        Returns:
            A specialized client for the capability.

        Raises:
            ClientError: If the capability is not supported.
        """
        # Check if we already have a client for this capability
        if capability in self._clients:
            return self._clients[capability]

        # Create client using CapabilityFactory (SpecializedClient handles ClientFactory internally)
        client = CapabilityFactory.create_client(self.model, capability)
        self._clients[capability] = client
        return client
    
    def has_capability(self, capability: Capability) -> bool:
        """Check if the model has a specific capability.
        
        Args:
            capability: The capability to check.
            
        Returns:
            True if the model has the capability, False otherwise.
        """
        return self.model.has_capability(capability)
    
    def get_available_capabilities(self) -> list[Capability]:
        """Get all capabilities available for this model.
        
        Returns:
            A list of capabilities supported by the model.
        """
        supported_capabilities = CapabilityFactory.get_supported_capabilities()
        return [cap for cap in supported_capabilities if self.model.has_capability(cap)]
    
    def chat(self, **kwargs) -> Dict:
        """Perform a chat conversation.
        
        Args:
            **kwargs: Chat parameters.
            
        Returns:
            The chat response.
        """
        client = self.get_client(Capability.CHAT_CONVERSATION)
        return self._execute_request(client, **kwargs)
    
    def embed(self, **kwargs) -> Dict:
        """Generate text embeddings.
        
        Args:
            **kwargs: Embedding parameters.
            
        Returns:
            The embedding response.
        """
        client = self.get_client(Capability.TEXT_EMBEDDING)
        return self._execute_request(client, **kwargs)
    
    def generate_image(self, **kwargs) -> Dict:
        """Generate an image.
        
        Args:
            **kwargs: Image generation parameters.
            
        Returns:
            The image generation response.
        """
        client = self.get_client(Capability.IMAGE_GENERATION)
        return self._execute_request(client, **kwargs)
    
    def transcribe_speech(self, **kwargs) -> Dict:
        """Transcribe speech to text.
        
        Args:
            **kwargs: Speech transcription parameters.
            
        Returns:
            The transcription response.
        """
        client = self.get_client(Capability.SPEECH_TO_TEXT)
        return self._execute_request(client, **kwargs)
    
    def _execute_request(self, client: SpecializedClient, **kwargs) -> Dict:
        """Execute a request using a specialized client.
        
        Args:
            client: The specialized client to use.
            **kwargs: Request parameters.
            
        Returns:
            The processed response.
        """
        # This will be implemented with actual HTTP request logic
        # For now, just validate and return the parameters
        client.validate_request(**kwargs)
        payload = client.build_request_payload(**kwargs)
        
        # TODO: Implement actual HTTP request
        # response = self._make_http_request(client, payload)
        # return client.process_response(response)
        
        return {"status": "mock_response", "payload": payload}
