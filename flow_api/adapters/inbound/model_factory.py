"""
Model factory module for flow_api.

This module provides a factory for creating capability-based client instances for specific models.
"""


from flow_api.core.models import AIModel
from flow_api.core.capabilities import Capability
from flow_api.core.model_registry import ModelRegistry
from flow_api.adapters.inbound.capability_factory import CapabilityClientManager
from flow_api.adapters.outbound.api.specialized_client import SpecializedClient
from flow_api.exceptions.client_error import ClientError


class ModelFactory:
    """Factory for creating capability-based client managers for specific models.

    This class provides a modern interface for working with models through
    capability-specific clients.
    """

    @classmethod
    def create_client_manager(cls, model_name: str) -> CapabilityClientManager:
        """Create a capability client manager for a specific model.

        Args:
            model_name: The name of the model.

        Returns:
            A capability client manager for the specified model.

        Raises:
            ClientError: If the model does not exist.
        """
        # Get the model from the registry
        model_registry = ModelRegistry()
        model = model_registry.get_model(model_name)

        if model is None:
            raise ClientError(f"Model '{model_name}' does not exist")

        return CapabilityClientManager(model)

    @classmethod
    def create_client_manager_for_model(cls, model: AIModel) -> CapabilityClientManager:
        """Create a capability client manager for a specific model.

        Args:
            model: The model.

        Returns:
            A capability client manager for the specified model.
        """
        return CapabilityClientManager(model)

    @classmethod
    def create_client_for_capability(cls, model_name: str, capability: Capability) -> SpecializedClient:
        """Create a specialized client for a specific model and capability.

        Args:
            model_name: The name of the model.
            capability: The required capability.

        Returns:
            A specialized client for the capability.

        Raises:
            ClientError: If the model doesn't exist or doesn't support the capability.
        """
        from flow_api.adapters.inbound.capability_factory import CapabilityFactory

        # Get the model from the registry
        model_registry = ModelRegistry()
        model = model_registry.get_model(model_name)

        if model is None:
            raise ClientError(f"Model '{model_name}' does not exist")

        return CapabilityFactory.create_client(model, capability)

    @classmethod
    def get_models_with_capability(cls, capability: Capability) -> list[AIModel]:
        """Get all models that support a specific capability.

        Args:
            capability: The required capability.

        Returns:
            A list of models that support the capability.
        """
        model_registry = ModelRegistry()
        return model_registry.get_models_by_capability(capability)