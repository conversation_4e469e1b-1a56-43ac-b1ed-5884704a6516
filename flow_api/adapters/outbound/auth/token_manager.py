"""
Token manager module for flow_api.

This module provides a token manager for authentication with the AI API.
"""

import json
import time
import threading
from typing import Optional, Dict, Any

import requests
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from flow_api.config import Config
from flow_api.exceptions.auth_error import AuthError
from flow_api.utils.logging import get_logger
from flow_api.utils.cache import LocalCache


logger = get_logger(__name__)


class TokenManager:
    """Token manager for authentication with the AI API.
    
    This class is responsible for obtaining and caching authentication tokens.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super(TokenManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the token manager."""
        if self._initialized:
            return

        self._token: Optional[str] = None
        self._token_expiration: Optional[float] = None
        self._config = Config()
        self._cache = LocalCache()
        self._initialized = True
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=5, min=5, max=15),
        retry=retry_if_exception_type(requests.RequestException)
    )
    def _obtain_token(self) -> None:
        """Obtain a new authentication token.
        
        This method is blocking and will retry up to 3 times with exponential backoff.
        
        Raises:
            AuthError: If the token cannot be obtained.
        """
        logger.info("Obtaining authentication token...")
        
        url = self._config.token_url
        headers = {
            'accept': '/',
            'Content-Type': 'application/json',
            'FlowTenant': self._config.flow_tenant
        }
        data = {
            "clientId": self._config.client_id,
            "clientSecret": self._config.client_secret,
            "appToAccess": self._config.app_to_access
        }
        
        try:
            response = requests.post(url, headers=headers, data=json.dumps(data))
            response.raise_for_status()
            token_data = response.json()
            self._token = token_data['access_token']

            # Cache the token for a bit less than its actual lifetime (3600 seconds)
            # to ensure we refresh it before it expires
            self._token_expiration = time.time() + self._config.token_cache_duration

            # Cache the token
            if self._token:
                self._cache.cache_token(self._token, self._config.token_cache_duration)

            # Fetch and cache models after successful token acquisition
            if self._token:
                try:
                    self._fetch_models(self._token)
                except AuthError as model_err:
                    # Log the error but don't fail token acquisition
                    logger.warning(f"Failed to fetch models: {model_err.message}")
            
        except requests.exceptions.HTTPError as err:
            obj = {}
            try:
                json_string = err.response.content.decode('utf-8')
                obj = json.loads(json_string)
            except ValueError:
                logger.error("Error decoding JSON response")
            
            message = obj.get('message', '[Error without message content]')
            activity_id = obj.get('activityId', '')
            
            if err.response.status_code in (400, 401):
                logger.error(f"Authentication error: {message}")
                raise AuthError(
                    message=f"Authentication error: {message}. Check your credentials.",
                    status_code=err.response.status_code,
                    activity_id=activity_id
                )
            
            if err.response.status_code == 500:
                logger.error(f"Internal server error: {message}")
                raise AuthError(
                    message=f"Internal server error: {message}",
                    status_code=err.response.status_code,
                    activity_id=activity_id
                )
            
            logger.error(f"Unexpected error: {err.response.text}")
            raise AuthError(
                message=f"Unexpected error: {message}",
                status_code=err.response.status_code,
                activity_id=activity_id
            )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=5, min=5, max=15),
        retry=retry_if_exception_type(requests.RequestException)
    )
    def _fetch_models(self, token: str) -> Dict[str, Any]:
        """Fetch available models from the API.

        Args:
            token: The authentication token.

        Returns:
            API response containing available models.

        Raises:
            AuthError: If the models cannot be fetched.
        """
        logger.info("Fetching available models...")

        url = f"{self._config.capabilities_url}/tenant/{self._config.flow_tenant}/capabilities"
        headers = {
            'accept': '*/*',
            'Authorization': f'Bearer {token}'
        }

        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            models_data = response.json()

            # Note: Models will be cached by ModelRegistry after sanitization
            logger.debug("Successfully fetched models")

            return models_data

        except requests.exceptions.HTTPError as err:
            obj = {}
            try:
                json_string = err.response.content.decode('utf-8')
                obj = json.loads(json_string)
            except ValueError:
                logger.error("Error decoding JSON response")

            message = obj.get('message', '[Error without message content]')
            activity_id = obj.get('activityId', '')

            if err.response.status_code in (400, 401):
                logger.error(f"Authentication error while fetching models: {message}")
                raise AuthError(
                    message=f"Authentication error while fetching models: {message}",
                    status_code=err.response.status_code,
                    activity_id=activity_id
                )

            if err.response.status_code == 500:
                logger.error(f"Internal server error while fetching models: {message}")
                raise AuthError(
                    message=f"Internal server error while fetching models: {message}",
                    status_code=err.response.status_code,
                    activity_id=activity_id
                )

            logger.error(f"Unexpected error while fetching models: {message}")
            raise AuthError(
                message=f"Unexpected error while fetching models: {message}",
                status_code=err.response.status_code,
                activity_id=activity_id
            )

        except requests.exceptions.RequestException as err:
            logger.error(f"Request error while fetching models: {str(err)}")
            raise AuthError(
                message=f"Request error while fetching models: {str(err)}",
                status_code=500,
                activity_id=""
            )

    def get_valid_token(self) -> str:
        """Get a valid authentication token.

        If the token is expired or not yet obtained, a new token will be obtained.
        This method is thread-safe and uses local cache when possible.

        Returns:
            A valid authentication token.

        Raises:
            AuthError: If the token cannot be obtained.
        """
        with self._lock:
            # First try to get token from cache
            cached_token = self._cache.get_cached_token()
            if cached_token:
                self._token = cached_token
                # Set a reasonable expiration time for cached tokens
                self._token_expiration = time.time() + self._config.token_cache_duration
                logger.debug("Using cached authentication token")
                return cached_token

            # If no cached token or cache expired, obtain new token
            if self._token is None or self._token_expiration is None or time.time() >= self._token_expiration:
                self._obtain_token()

            # Ensure token was successfully obtained
            if self._token is None:
                raise AuthError(
                    message="Failed to obtain authentication token",
                    status_code=500,
                    activity_id=""
                )

            return self._token

    def get_cached_models(self) -> Dict[str, Any] | None:
        """Get cached models if available.

        Returns:
            API response with cached models if available, None otherwise.
        """
        return self._cache.get_cached_models()

    def refresh_models(self) -> Dict[str, Any]:
        """Force refresh of models from the API.

        Returns:
            API response containing available models.

        Raises:
            AuthError: If models cannot be fetched.
        """
        token = self.get_valid_token()
        return self._fetch_models(token)

    def check_connection(self) -> Dict[str, Any]:
        """Check the connection status and ensure a valid token is available.

        This method allows users to verify the connection before making API calls,
        avoiding delays during actual requests. It will obtain a new token if needed.

        Returns:
            A dictionary containing connection status information:
            - 'status': 'connected' if successful, 'error' if failed
            - 'token_source': 'cache' if using cached token, 'new' if obtained new token
            - 'message': Human-readable status message
            - 'error': Error message if status is 'error' (optional)

        """
        try:
            with self._lock:
                # First check if we have a cached token
                cached_token = self._cache.get_cached_token()
                if cached_token:
                    self._token = cached_token
                    self._token_expiration = time.time() + self._config.token_cache_duration
                    logger.info("Connection verified: Using cached authentication token")
                    return {
                        'status': 'connected',
                        'token_source': 'cache',
                        'message': 'Connection verified successfully using cached token'
                    }

                # If no cached token or cache expired, obtain new token
                if self._token is None or self._token_expiration is None or time.time() >= self._token_expiration:
                    logger.info("Verifying connection: Obtaining new authentication token...")
                    self._obtain_token()

                # Ensure token was successfully obtained
                if self._token is None:
                    error_msg = "Failed to obtain authentication token"
                    logger.error(f"Connection check failed: {error_msg}")
                    return {
                        'status': 'error',
                        'token_source': 'none',
                        'message': 'Connection check failed',
                        'error': error_msg
                    }

                logger.info("Connection verified: New authentication token obtained successfully")
                return {
                    'status': 'connected',
                    'token_source': 'new',
                    'message': 'Connection verified successfully with new token'
                }

        except AuthError as auth_err:
            logger.error(f"Connection check failed: {auth_err.message}")
            return {
                'status': 'error',
                'token_source': 'none',
                'message': 'Connection check failed due to authentication error',
                'error': auth_err.message
            }
        except Exception as err:
            error_msg = f"Unexpected error during connection check: {str(err)}"
            logger.error(error_msg)
            return {
                'status': 'error',
                'token_source': 'none',
                'message': 'Connection check failed due to unexpected error',
                'error': error_msg
            }