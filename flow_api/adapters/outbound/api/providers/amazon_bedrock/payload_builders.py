"""
Amazon Bedrock payload builders for flow_api.

This module provides payload builders for Amazon Bedrock API requests.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.payload_builder import PayloadBuilder


class BedrockPayloadBuilder(PayloadBuilder):
    """Payload builder for Amazon Bedrock API requests."""
    
    def build_chat_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Amazon Bedrock chat request payload."""
        messages = kwargs.get("messages", [])

        # Different formats for different Bedrock models
        if "claude" in self.model.name.lower():
            return self._build_claude_payload(messages, kwargs)
        elif "nova" in self.model.name.lower():
            return self._build_nova_payload(messages, kwargs)
        elif "llama" in self.model.name.lower():
            return self._build_llama_payload(messages, kwargs)
        elif "titan" in self.model.name.lower():
            return self._build_titan_payload(messages, kwargs)
        else:
            # Default Bedrock format
            return {
                "messages": messages,
                "inferenceConfig": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "maxTokens": kwargs.get("max_tokens", 1000)
                },
                "allowedModels": [self.model.name]
            }

    def _build_claude_payload(self, messages: list, params: dict) -> Dict[str, Any]:
        """Build Claude-specific payload for Bedrock."""
        # Convert messages to Claude format with content array
        claude_messages = []
        for message in messages:
            if message.get("role") == "user":
                # Convert user message to Claude format
                content = message.get("content", "")
                if isinstance(content, str):
                    claude_content = [{"type": "text", "text": content}]
                else:
                    claude_content = content

                claude_messages.append({
                    "role": "user",
                    "content": claude_content
                })
            else:
                # Keep other messages as-is
                claude_messages.append(message)

        payload = {
            "messages": claude_messages,
            "anthropic_version": "bedrock-2023-05-31",
            "allowedModels": [self.model.name]
        }

        # Add parameters with defaults matching the curl
        payload["max_tokens"] = params.get("max_tokens", 1000)
        payload["temperature"] = params.get("temperature", 1.0)
        payload["top_p"] = params.get("top_p", 0.999)
        payload["top_k"] = params.get("top_k", 250)
        payload["stop_sequences"] = params.get("stop_sequences", [])

        return payload

    def _build_nova_payload(self, messages: list, params: dict) -> Dict[str, Any]:
        """Build Nova-specific payload for Bedrock."""
        # Convert messages to Nova format with content array
        nova_messages = []
        for message in messages:
            if message.get("role") == "user":
                # Convert user message to Nova format
                content = message.get("content", "")
                if isinstance(content, str):
                    nova_content = [{"text": content}]
                else:
                    nova_content = content

                nova_messages.append({
                    "role": "user",
                    "content": nova_content
                })
            else:
                # Keep other messages as-is
                nova_messages.append(message)

        payload = {
            "messages": nova_messages,
            "allowedModels": [self.model.name]
        }

        # Add inference config
        inference_config = {}
        if "max_tokens" in params:
            inference_config["max_new_tokens"] = params["max_tokens"]
        if "temperature" in params:
            inference_config["temperature"] = params["temperature"]
        if "top_p" in params:
            inference_config["topP"] = params["top_p"]

        if inference_config:
            payload["inferenceConfig"] = inference_config

        return payload

    def _build_llama_payload(self, messages: list, params: dict) -> Dict[str, Any]:
        """Build Llama-specific payload for Bedrock."""
        # Llama uses simple prompt instead of messages
        prompt = ""
        for message in messages:
            content = message.get("content", "")
            # For Llama, just concatenate the content without role prefixes
            if content:
                prompt += content + " "

        payload = {
            "prompt": prompt.strip(),
            "allowedModels": [self.model.name]
        }

        # Add optional parameters (Llama supports these)
        if "temperature" in params:
            payload["temperature"] = params["temperature"]
        if "max_tokens" in params:
            payload["max_gen_len"] = params["max_tokens"]  # Llama uses max_gen_len
        if "top_p" in params:
            payload["top_p"] = params["top_p"]

        return payload

    def _build_titan_payload(self, messages: list, params: dict) -> Dict[str, Any]:
        """Build Titan-specific payload."""
        # Titan uses a different format with inputText
        prompt = ""
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            prompt += f"{role}: {content}\n"

        return {
            "inputText": prompt.strip(),
            "textGenerationConfig": {
                "temperature": params.get("temperature", 0.7),
                "topP": params.get("top_p", 0.9),
                "maxTokenCount": params.get("max_tokens", 1000)
            },
            "allowedModels": [self.model.name]
        }
    
    def build_embedding_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Amazon Bedrock embedding request payload."""
        if "titan" in self.model.name.lower():
            return {
                "inputText": kwargs.get("input", ""),
                "allowedModels": [self.model.name]
            }
        else:
            # Default Bedrock embedding format
            return {
                "texts": [kwargs.get("input", "")],
                "allowedModels": [self.model.name]
            }
