"""
Amazon Bedrock embedding client for flow_api.

This module provides the embedding client for Amazon Bedrock services.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_embedding_client import BaseEmbeddingClient
from flow_api.adapters.outbound.api.providers.amazon_bedrock.payload_builders import BedrockPayloadBuilder
from flow_api.config import Config


class BedrockEmbeddingClient(BaseEmbeddingClient):
    """Embedding client for Amazon Bedrock services."""
    
    def __init__(self, model):
        """Initialize the Amazon Bedrock embedding client.

        Args:
            model: The AI model to use.
        """
        super().__init__(model)
        self._payload_builder = BedrockPayloadBuilder(model)
        self._config = Config()

    def get_endpoint_path(self) -> str:
        """Get the endpoint path for Amazon Bedrock embedding requests."""
        return self._config.get_endpoint_path("amazon_bedrock", "embedding")
    
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Amazon Bedrock embedding request payload."""
        return self._payload_builder.build_embedding_payload(**kwargs)
