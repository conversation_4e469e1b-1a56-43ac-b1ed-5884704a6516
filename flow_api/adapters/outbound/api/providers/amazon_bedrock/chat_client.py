"""
Amazon Bedrock chat client for flow_api.

This module provides the chat client for Amazon Bedrock services.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_chat_client import BaseChatClient
from flow_api.adapters.outbound.api.providers.amazon_bedrock.payload_builders import BedrockPayloadBuilder
from flow_api.config import Config


class BedrockChatClient(BaseChatClient):
    """Chat client for Amazon Bedrock services."""
    
    def __init__(self, model):
        """Initialize the Amazon Bedrock chat client.

        Args:
            model: The AI model to use.
        """
        super().__init__(model)
        self._payload_builder = BedrockPayloadBuilder(model)
        self._config = Config()

    def get_endpoint_path(self) -> str:
        """Get the endpoint path for Amazon Bedrock chat requests."""
        return self._config.get_endpoint_path("amazon_bedrock", "chat")
    
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Amazon Bedrock chat request payload."""
        return self._payload_builder.build_chat_payload(**kwargs)
