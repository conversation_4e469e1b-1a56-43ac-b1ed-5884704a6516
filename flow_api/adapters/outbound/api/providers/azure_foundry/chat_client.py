"""
Azure Foundry chat client for flow_api.

This module provides the chat client for Azure Foundry services.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_chat_client import BaseChatClient
from flow_api.adapters.outbound.api.providers.azure_foundry.payload_builders import AzureFoundryPayloadBuilder
from flow_api.config import Config


class AzureFoundryChatClient(BaseChatClient):
    """Chat client for Azure Foundry services."""
    
    def __init__(self, model):
        """Initialize the Azure Foundry chat client.

        Args:
            model: The AI model to use.
        """
        super().__init__(model)
        self._payload_builder = AzureFoundryPayloadBuilder(model)
        self._config = Config()

    def get_endpoint_path(self) -> str:
        """Get the endpoint path for Azure Foundry chat requests."""
        return self._config.get_endpoint_path("azure_foundry", "chat")
    
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure Foundry chat request payload."""
        return self._payload_builder.build_chat_payload(**kwargs)
