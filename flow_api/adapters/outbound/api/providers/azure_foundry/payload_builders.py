"""
Azure Foundry payload builders for flow_api.

This module provides payload builders for Azure Foundry API requests.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.payload_builder import PayloadBuilder


class AzureFoundryPayloadBuilder(PayloadBuilder):
    """Payload builder for Azure Foundry API requests."""
    
    def build_chat_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure Foundry chat request payload."""
        payload = {
            "messages": kwargs.get("messages", []),
            "allowedModels": [self.model.name]
        }

        # Add optional parameters
        if "temperature" in kwargs:
            payload["temperature"] = kwargs["temperature"]
        if "max_tokens" in kwargs:
            payload["max_tokens"] = kwargs["max_tokens"]
        if "top_p" in kwargs:
            payload["top_p"] = kwargs["top_p"]
        if "frequency_penalty" in kwargs:
            payload["frequency_penalty"] = kwargs["frequency_penalty"]
        if "presence_penalty" in kwargs:
            payload["presence_penalty"] = kwargs["presence_penalty"]
        if "stream" in kwargs:
            payload["stream"] = kwargs["stream"]
        if "stop" in kwargs:
            payload["stop"] = kwargs["stop"]
        if "seed" in kwargs:
            payload["seed"] = kwargs["seed"]

        return payload
    
    def build_embedding_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure Foundry embedding request payload."""
        payload = {
            "allowedModels": [self.model.name],
            "input": kwargs.get("input", "")
        }

        if "encoding_format" in kwargs:
            payload["encoding_format"] = kwargs["encoding_format"]
        if "dimensions" in kwargs:
            payload["dimensions"] = kwargs["dimensions"]
        if "user" in kwargs:
            payload["user"] = kwargs["user"]

        return payload
