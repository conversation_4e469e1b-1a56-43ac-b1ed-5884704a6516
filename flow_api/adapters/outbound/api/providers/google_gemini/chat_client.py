"""
Google Gemini chat client for flow_api.

This module provides the chat client for Google Gemini services.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_chat_client import BaseChatClient
from flow_api.adapters.outbound.api.providers.google_gemini.payload_builders import GeminiPayloadBuilder
from flow_api.config import Config


class GeminiChatClient(BaseChatClient):
    """Chat client for Google Gemini services."""
    
    def __init__(self, model):
        """Initialize the Google Gemini chat client.

        Args:
            model: The AI model to use.
        """
        super().__init__(model)
        self._payload_builder = GeminiPayloadBuilder(model)
        self._config = Config()

    def get_endpoint_path(self) -> str:
        """Get the endpoint path for Google Gemini chat requests."""
        return self._config.get_endpoint_path("google_gemini", "chat")
    
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Google Gemini chat request payload."""
        return self._payload_builder.build_chat_payload(**kwargs)
    
    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process Gemini response to OpenAI format."""
        if 'candidates' not in response_data:
            return response_data
        
        # Convert Gemini format to OpenAI format
        choices = []
        for candidate in response_data.get('candidates', []):
            content_obj = candidate.get('content', {})
            parts = content_obj.get('parts', [])
            
            # Combine all text parts
            content = ""
            for part in parts:
                if 'text' in part:
                    content += part['text']
            
            choice = {
                "message": {
                    "role": "assistant",
                    "content": content
                },
                "finish_reason": candidate.get('finishReason', 'stop').lower()
            }
            choices.append(choice)
        
        return {
            "choices": choices,
            "model": response_data.get("modelVersion", self.model.name),
            "usage": self._convert_gemini_usage(response_data.get("usageMetadata", {}))
        }
    
    def _convert_gemini_usage(self, usage_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Gemini usage metadata to OpenAI format."""
        return {
            "prompt_tokens": usage_metadata.get("promptTokenCount", 0),
            "completion_tokens": usage_metadata.get("candidatesTokenCount", 0),
            "total_tokens": usage_metadata.get("totalTokenCount", 0)
        }
