"""
Google Gemini image recognition client for flow_api.

This module provides the image recognition client for Google Gemini services.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_image_recognition_client import BaseImageRecognitionClient
from flow_api.adapters.outbound.api.providers.google_gemini.payload_builders import GeminiPayloadBuilder
from flow_api.config import Config


class GeminiImageRecognitionClient(BaseImageRecognitionClient):
    """Image recognition client for Google Gemini services."""
    
    def __init__(self, model):
        """Initialize the Google Gemini image recognition client.
        
        Args:
            model: The AI model to use.
        """
        super().__init__(model)
        self._payload_builder = GeminiPayloadBuilder(model)
        self._config = Config()
    
    def get_endpoint_path(self) -> str:
        """Get the endpoint path for Google Gemini image recognition requests."""
        return self._config.get_endpoint_path("google_gemini", "image_recognition")
    
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Google Gemini image recognition request payload."""
        return self._payload_builder.build_chat_payload(**kwargs)
