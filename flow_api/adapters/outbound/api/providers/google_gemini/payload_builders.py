"""
Google Gemini payload builders for flow_api.

This module provides payload builders for Google Gemini API requests.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.payload_builder import PayloadBuilder


class GeminiPayloadBuilder(PayloadBuilder):
    """Payload builder for Google Gemini API requests."""
    
    def build_chat_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Google Gemini chat request payload."""
        messages = kwargs.get("messages", [])
        
        # Convert messages to Gemini format
        gemini_contents = []
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            # Gemini uses "user" and "model" roles
            gemini_role = "user" if role == "user" else "model"
            
            gemini_contents.append({
                "role": gemini_role,
                "parts": [{"text": content}]
            })
        
        payload = {
            "contents": gemini_contents,
            "allowedModels": [self.model.name]
        }
        
        # Add generation config
        generation_config = {}
        if "temperature" in kwargs:
            generation_config["temperature"] = kwargs["temperature"]
        if "max_tokens" in kwargs:
            generation_config["maxOutputTokens"] = kwargs["max_tokens"]
        if "top_p" in kwargs:
            generation_config["topP"] = kwargs["top_p"]
        if "top_k" in kwargs:
            generation_config["topK"] = kwargs["top_k"]
        
        if generation_config:
            payload["generationConfig"] = generation_config
        
        # Add safety settings if provided
        if "safety_settings" in kwargs:
            payload["safetySettings"] = kwargs["safety_settings"]
        
        return payload
    
    def build_embedding_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Google Gemini embedding request payload."""
        payload = {
            "content": {
                "parts": [{"text": kwargs.get("input", "")}]
            },
            "allowedModels": [self.model.name]
        }
        
        if "task_type" in kwargs:
            payload["taskType"] = kwargs["task_type"]
        if "title" in kwargs:
            payload["title"] = kwargs["title"]
        
        return payload
