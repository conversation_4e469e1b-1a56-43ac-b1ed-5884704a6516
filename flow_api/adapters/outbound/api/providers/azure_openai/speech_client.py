"""
Azure OpenAI speech-to-text client for flow_api.

This module provides the speech-to-text client for Azure OpenAI services.
"""

from typing import Dict, Any
import json
import base64

from flow_api.adapters.outbound.api.base.base_speech_client import BaseSpeechClient
from flow_api.adapters.outbound.api.providers.azure_openai.payload_builders import AzureOpenAIPayloadBuilder
from flow_api.config import Config


class AzureOpenAISpeechClient(BaseSpeechClient):
    """Speech-to-text client for Azure OpenAI services."""

    def __init__(self, model):
        """Initialize the Azure OpenAI speech client.

        Args:
            model: The AI model to use.
        """
        super().__init__(model)
        self._payload_builder = AzureOpenAIPayloadBuilder(model)
        self._config = Config()

    def get_endpoint_path(self) -> str:
        """Get the endpoint path for Azure OpenAI speech requests."""
        return self._config.get_endpoint_path("azure_openai", "speech")

    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure OpenAI speech request payload for multipart/form-data."""
        # For speech-to-text, we need to prepare multipart form data
        form_data = {}

        # Model field (as regular form field)
        form_data['model'] = self.model.name

        # Definition with default settings (as regular form field)
        definition = {
            "diarization": {
                "enabled": True,
                "maxSpeakers": 2
            },
            "locales": ["en-US"],
            "profanityFilterMode": "Masked"
        }
        form_data['definition'] = json.dumps(definition)

        # Inline processing (as regular form field)
        form_data['inline'] = 'true'

        # Audio file - convert base64 to binary if needed
        if 'file' in kwargs:
            audio_data = kwargs['file']
            if isinstance(audio_data, str):
                # Assume it's base64 encoded
                try:
                    audio_binary = base64.b64decode(audio_data)
                    form_data['audio'] = ('audio.wav', audio_binary, 'audio/wav')
                except Exception:
                    # If not base64, treat as binary
                    form_data['audio'] = ('audio.wav', audio_data.encode() if isinstance(audio_data, str) else audio_data, 'audio/wav')
            else:
                form_data['audio'] = ('audio.wav', audio_data, 'audio/wav')

        return form_data

    def requires_multipart(self) -> bool:
        """Indicate that this client requires multipart/form-data."""
        return True
