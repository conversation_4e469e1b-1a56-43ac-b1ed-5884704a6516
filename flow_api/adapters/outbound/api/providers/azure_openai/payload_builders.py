"""
Azure OpenAI payload builders for flow_api.

This module provides payload builders for Azure OpenAI API requests.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.payload_builder import PayloadBuilder


class AzureOpenAIPayloadBuilder(PayloadBuilder):
    """Payload builder for Azure OpenAI API requests."""
    
    def build_chat_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure OpenAI chat request payload."""
        payload = {
            "messages": kwargs.get("messages", [])
        }

        # Handle model specification based on model type
        if self.model.name == "o1":
            # o1 model uses "model" field as string
            payload["model"] = "o1"
        elif self.model.name in ["o1-mini", "o3-mini"]:
            # o1-mini and o3-mini use "allowedModels" field
            payload["allowedModels"] = [self.model.name]
        else:
            # Other models use "allowedModels" field
            payload["allowedModels"] = [self.model.name]

        # Add optional parameters based on model type
        if self.model.name in ["o1", "o1-mini", "o3-mini"]:
            # o1/o3 models use different parameter names and have reasoning_effort
            if "max_tokens" in kwargs:
                payload["max_completion_tokens"] = kwargs["max_tokens"]
            if "top_p" in kwargs:
                payload["top_p"] = kwargs["top_p"]
            if self.model.name in ["o1", "o3-mini"]:
                # o1 and o3-mini support reasoning_effort (o1-mini doesn't)
                payload["reasoning_effort"] = kwargs.get("reasoning_effort", "low")
            # o1/o3 models don't support temperature or stream
        else:
            # Standard models
            if "temperature" in kwargs:
                payload["temperature"] = kwargs["temperature"]
            if "max_tokens" in kwargs:
                payload["max_tokens"] = kwargs["max_tokens"]
            if "top_p" in kwargs:
                payload["top_p"] = kwargs["top_p"]
            if "frequency_penalty" in kwargs:
                payload["frequency_penalty"] = kwargs["frequency_penalty"]
            if "presence_penalty" in kwargs:
                payload["presence_penalty"] = kwargs["presence_penalty"]
            if "stream" in kwargs:
                payload["stream"] = kwargs["stream"]
            if "stop" in kwargs:
                payload["stop"] = kwargs["stop"]
            if "seed" in kwargs:
                payload["seed"] = kwargs["seed"]

        return payload
    
    def build_embedding_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure OpenAI embedding request payload."""
        payload = {
            "allowedModels": [self.model.name],
            "input": kwargs.get("input", "")
        }

        if "encoding_format" in kwargs:
            payload["encoding_format"] = kwargs["encoding_format"]
        if "dimensions" in kwargs:
            payload["dimensions"] = kwargs["dimensions"]
        if "user" in kwargs:
            payload["user"] = kwargs["user"]

        return payload
    
    def build_image_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure OpenAI image generation request payload."""
        payload = {
            "model": self.model.name,
            "prompt": kwargs.get("prompt", "")
        }

        if "n" in kwargs:
            payload["n"] = kwargs["n"]
        if "size" in kwargs:
            payload["size"] = kwargs["size"]
        if "quality" in kwargs:
            payload["quality"] = kwargs["quality"]
        if "style" in kwargs:
            payload["style"] = kwargs["style"]
        if "response_format" in kwargs:
            payload["response_format"] = kwargs["response_format"]
        if "user" in kwargs:
            payload["user"] = kwargs["user"]

        return payload

    def build_speech_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure OpenAI speech-to-text request payload."""
        payload = {
            "model": self.model.name,
            "file": kwargs.get("file", "")
        }

        if "language" in kwargs:
            payload["language"] = kwargs["language"]
        if "prompt" in kwargs:
            payload["prompt"] = kwargs["prompt"]
        if "response_format" in kwargs:
            payload["response_format"] = kwargs["response_format"]
        if "temperature" in kwargs:
            payload["temperature"] = kwargs["temperature"]

        return payload
