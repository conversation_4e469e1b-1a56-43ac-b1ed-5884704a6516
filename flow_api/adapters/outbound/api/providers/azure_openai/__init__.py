"""
Azure OpenAI provider module for flow_api.

This module provides clients and payload builders for Azure OpenAI services.
"""

from flow_api.adapters.outbound.api.providers.azure_openai.chat_client import AzureOpenAIChatClient
from flow_api.adapters.outbound.api.providers.azure_openai.embedding_client import AzureOpenAIEmbeddingClient
from flow_api.adapters.outbound.api.providers.azure_openai.image_generation_client import AzureOpenAIImageGenerationClient
from flow_api.adapters.outbound.api.providers.azure_openai.image_recognition_client import AzureOpenAIImageRecognitionClient
from flow_api.adapters.outbound.api.providers.azure_openai.speech_client import AzureOpenAISpeechClient
from flow_api.adapters.outbound.api.providers.azure_openai.payload_builders import AzureOpenAIPayloadBuilder

__all__ = [
    "AzureOpenAIChatClient",
    "AzureOpenAIEmbeddingClient",
    "AzureOpenAIImageGenerationClient",
    "AzureOpenAIImageRecognitionClient",
    "AzureOpenAISpeechClient",
    "AzureOpenAIPayloadBuilder"
]
