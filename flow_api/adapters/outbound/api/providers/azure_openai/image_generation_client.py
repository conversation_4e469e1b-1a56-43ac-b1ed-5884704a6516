"""
Azure OpenAI image generation client for flow_api.

This module provides the image generation client for Azure OpenAI services.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_image_generation_client import BaseImageGenerationClient
from flow_api.adapters.outbound.api.providers.azure_openai.payload_builders import AzureOpenAIPayloadBuilder
from flow_api.config import Config


class AzureOpenAIImageGenerationClient(BaseImageGenerationClient):
    """Image generation client for Azure OpenAI services."""
    
    def __init__(self, model):
        """Initialize the Azure OpenAI image client.

        Args:
            model: The AI model to use.
        """
        super().__init__(model)
        self._payload_builder = AzureOpenAIPayloadBuilder(model)
        self._config = Config()

    def get_endpoint_path(self) -> str:
        """Get the endpoint path for Azure OpenAI image generation requests."""
        return self._config.get_endpoint_path("azure_openai", "image_generation")
    
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure OpenAI image generation request payload."""
        return self._payload_builder.build_image_payload(**kwargs)
