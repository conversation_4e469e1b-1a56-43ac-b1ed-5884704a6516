"""
Azure OpenAI embedding client for flow_api.

This module provides the embedding client for Azure OpenAI services.
"""

from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_embedding_client import BaseEmbeddingClient
from flow_api.adapters.outbound.api.providers.azure_openai.payload_builders import AzureOpenAIPayloadBuilder
from flow_api.config import Config


class AzureOpenAIEmbeddingClient(BaseEmbeddingClient):
    """Embedding client for Azure OpenAI services."""
    
    def __init__(self, model):
        """Initialize the Azure OpenAI embedding client.

        Args:
            model: The AI model to use.
        """
        super().__init__(model)
        self._payload_builder = AzureOpenAIPayloadBuilder(model)
        self._config = Config()

    def get_endpoint_path(self) -> str:
        """Get the endpoint path for Azure OpenAI embedding requests."""
        return self._config.get_endpoint_path("azure_openai", "embedding")
    
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build Azure OpenAI embedding request payload."""
        return self._payload_builder.build_embedding_payload(**kwargs)
