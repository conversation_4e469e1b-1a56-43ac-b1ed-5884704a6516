"""
Specialized client module for flow_api.

This module provides capability-specific clients that coordinate with provider-specific implementations.
"""

from typing import Dict, Any, Optional
import logging

from flow_api.core.models import AIModel
from flow_api.core.capabilities import Capability
from flow_api.adapters.outbound.api.client_factory import ClientFactory

logger = logging.getLogger(__name__)


class SpecializedClient:
    """Base class for specialized clients that coordinate with provider-specific implementations."""

    def __init__(self, model: AIModel, capability: Optional[Capability] = None):
        """Initialize the specialized client.

        Args:
            model: The AI model to use.
            capability: The capability required. If None, subclasses should set it.
        """
        self.model = model
        self.capability = capability or self._get_default_capability()
        self._provider_client = ClientFactory.create_client(model, self.capability)

    def _get_default_capability(self) -> Capability:
        """Get the default capability for this client. Subclasses should override."""
        raise NotImplementedError("Subclasses must implement _get_default_capability or pass capability")
    
    def get_endpoint_path(self) -> str:
        """Get the endpoint path for this client."""
        return self._provider_client.get_endpoint_path()
    
    def validate_request(self, **kwargs) -> None:
        """Validate the request parameters."""
        return self._provider_client.validate_request(**kwargs)
    
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build the request payload."""
        return self._provider_client.build_request_payload(**kwargs)
    
    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process the API response."""
        return self._provider_client.process_response(response_data)

    def requires_multipart(self) -> bool:
        """Check if this client requires multipart/form-data.

        Returns:
            True if the underlying provider client requires multipart, False otherwise.
        """
        if hasattr(self._provider_client, 'requires_multipart'):
            return self._provider_client.requires_multipart()
        return False


class ChatClient(SpecializedClient):
    """Specialized client for chat conversation capabilities."""

    def __init__(self, model: AIModel):
        super().__init__(model)

    def _get_default_capability(self) -> Capability:
        """Get the default capability for chat clients."""
        return Capability.CHAT_CONVERSATION


class EmbeddingClient(SpecializedClient):
    """Specialized client for text embedding capabilities."""

    def __init__(self, model: AIModel):
        super().__init__(model)

    def _get_default_capability(self) -> Capability:
        """Get the default capability for embedding clients."""
        return Capability.TEXT_EMBEDDING


class ImageGenerationClient(SpecializedClient):
    """Specialized client for image generation capabilities."""

    def __init__(self, model: AIModel):
        super().__init__(model)

    def _get_default_capability(self) -> Capability:
        """Get the default capability for image generation clients."""
        return Capability.IMAGE_GENERATION


class SpeechClient(SpecializedClient):
    """Specialized client for speech-to-text capabilities."""

    def __init__(self, model: AIModel):
        super().__init__(model)

    def _get_default_capability(self) -> Capability:
        """Get the default capability for speech clients."""
        return Capability.SPEECH_TO_TEXT
