"""FlowAPIClient factory module for flow_api."""

from typing import Type, Dict
from flow_api.core.models import AIModel
from flow_api.core.capabilities import Capability
from flow_api.adapters.outbound.api.base.base_api_client import BaseAPIClient
from flow_api.exceptions.client_error import ClientError

# Azure OpenAI imports
from flow_api.adapters.outbound.api.providers.azure_openai.chat_client import AzureOpenAIChatClient
from flow_api.adapters.outbound.api.providers.azure_openai.embedding_client import AzureOpenAIEmbeddingClient
from flow_api.adapters.outbound.api.providers.azure_openai.image_generation_client import AzureOpenAIImageGenerationClient
from flow_api.adapters.outbound.api.providers.azure_openai.image_recognition_client import AzureOpenAIImageRecognitionClient
from flow_api.adapters.outbound.api.providers.azure_openai.speech_client import AzureOpenAISpeechClient

# Amazon Bedrock imports
from flow_api.adapters.outbound.api.providers.amazon_bedrock.chat_client import BedrockChat<PERSON>lient
from flow_api.adapters.outbound.api.providers.amazon_bedrock.embedding_client import Bedrock<PERSON>mbedding<PERSON>lient

# Google Gemini imports
from flow_api.adapters.outbound.api.providers.google_gemini.chat_client import GeminiChatClient
from flow_api.adapters.outbound.api.providers.google_gemini.embedding_client import GeminiEmbeddingClient

# Azure Foundry imports
from flow_api.adapters.outbound.api.providers.azure_foundry.chat_client import AzureFoundryChatClient


class ClientFactory:
    """Factory for creating provider-specific clients based on model and capability."""
    
    # Mapping of (provider, capability) to client class
    _client_mapping: Dict[tuple, Type[BaseAPIClient]] = {
        # Azure OpenAI
        ("azure-openai", Capability.CHAT_CONVERSATION): AzureOpenAIChatClient,
        ("azure-openai", Capability.IMAGE_RECOGNITION): AzureOpenAIImageRecognitionClient,
        ("azure-openai", Capability.TEXT_EMBEDDING): AzureOpenAIEmbeddingClient,
        ("azure-openai", Capability.IMAGE_GENERATION): AzureOpenAIImageGenerationClient,
        ("azure-openai", Capability.SPEECH_TO_TEXT): AzureOpenAISpeechClient,

        # Amazon Bedrock
        ("amazon-bedrock", Capability.CHAT_CONVERSATION): BedrockChatClient,
        ("amazon-bedrock", Capability.IMAGE_RECOGNITION): BedrockChatClient,
        ("amazon-bedrock", Capability.TEXT_EMBEDDING): BedrockEmbeddingClient,

        # Google Gemini
        ("google-gemini", Capability.CHAT_CONVERSATION): GeminiChatClient,
        ("google-gemini", Capability.IMAGE_RECOGNITION): GeminiChatClient,
        ("google-gemini", Capability.TEXT_EMBEDDING): GeminiEmbeddingClient,

        # Azure Foundry
        ("azure-foundry", Capability.CHAT_CONVERSATION): AzureFoundryChatClient,
        ("azure-foundry", Capability.IMAGE_RECOGNITION): AzureFoundryChatClient,

        # Azure AI Speech
        ("azure-ai-speech", Capability.SPEECH_TO_TEXT): AzureOpenAISpeechClient,
    }
    
    @classmethod
    def create_client(cls, model: AIModel, capability: Capability) -> BaseAPIClient:
        """Create a client for the specified model and capability."""
        if not model.has_capability(capability):
            raise ClientError(f"Model '{model.name}' does not support capability '{capability.value}'")
        
        key = (model.provider.value, capability)
        client_class = cls._client_mapping.get(key)
        
        if client_class is None:
            raise ClientError(f"No client available for provider '{model.provider.value}' and capability '{capability.value}'")
        
        return client_class(model)
