"""
Base image recognition client module for flow_api.

This module provides the base class for image recognition clients.
"""

from abc import abstractmethod
from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_api_client import BaseAPIClient
from flow_api.exceptions.client_error import ClientError


class BaseImageRecognitionClient(BaseAPIClient):
    """Base class for image recognition clients.
    
    This class provides common functionality for image recognition clients across different providers.
    """
    
    def validate_request(self, **kwargs) -> None:
        """Validate image recognition request parameters.
        
        Args:
            **kwargs: Request parameters to validate.
            
        Raises:
            ClientError: If validation fails.
        """
        if 'messages' not in kwargs:
            raise ClientError("Image recognition request must include messages")
        
        # Check if at least one message contains an image
        messages = kwargs.get('messages', [])
        has_image = False
        
        for message in messages:
            if isinstance(message.get('content'), list):
                for content_item in message['content']:
                    if content_item.get('type') == 'image_url':
                        has_image = True
                        break
            if has_image:
                break
        
        if not has_image:
            raise ClientError("Image recognition request must include at least one image in messages")
    
    @abstractmethod
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build image recognition request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The image recognition request payload.
        """
        pass
