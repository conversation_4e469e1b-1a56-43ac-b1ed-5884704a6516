"""
Base image generation client module for flow_api.

This module provides the base class for image generation clients.
"""

from abc import abstractmethod
from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_api_client import BaseAPIClient
from flow_api.exceptions.client_error import ClientError


class BaseImageGenerationClient(BaseAPIClient):
    """Base class for image generation clients.
    
    This class provides common functionality for image generation clients across different providers.
    """
    
    def validate_request(self, **kwargs) -> None:
        """Validate image generation request parameters.
        
        Args:
            **kwargs: Request parameters to validate.
            
        Raises:
            ClientError: If validation fails.
        """
        if 'prompt' not in kwargs:
            raise ClientError("Image generation request must include prompt")
    
    @abstractmethod
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build image generation request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The image generation request payload.
        """
        pass
