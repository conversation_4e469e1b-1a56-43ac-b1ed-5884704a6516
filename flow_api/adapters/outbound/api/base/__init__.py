"""
Base classes for API clients.

This module provides abstract base classes and interfaces for API clients.
"""

from flow_api.adapters.outbound.api.base.base_api_client import BaseAPIClient
from flow_api.adapters.outbound.api.base.base_chat_client import BaseChatClient
from flow_api.adapters.outbound.api.base.base_embedding_client import BaseEmbedding<PERSON>lient
from flow_api.adapters.outbound.api.base.base_image_generation_client import BaseImageGenerationClient
from flow_api.adapters.outbound.api.base.base_image_recognition_client import BaseImageRecognitionClient
from flow_api.adapters.outbound.api.base.base_speech_client import BaseSpeech<PERSON>lient
from flow_api.adapters.outbound.api.base.payload_builder import PayloadBuilder

__all__ = [
    "BaseAPIClient",
    "BaseChatClient",
    "BaseEmbeddingClient",
    "BaseImageGenerationClient",
    "BaseImageRecognitionClient",
    "BaseSpeechClient",
    "PayloadBuilder"
]
