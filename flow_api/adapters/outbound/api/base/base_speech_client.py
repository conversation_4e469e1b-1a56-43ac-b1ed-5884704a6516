"""
Base speech-to-text client module for flow_api.

This module provides the base class for speech-to-text clients.
"""

from abc import abstractmethod
from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_api_client import BaseAPIClient
from flow_api.exceptions.client_error import ClientError


class BaseSpeechClient(BaseAPIClient):
    """Base class for speech-to-text clients.
    
    This class provides common functionality for speech clients across different providers.
    """
    
    def validate_request(self, **kwargs) -> None:
        """Validate speech request parameters.

        Args:
            **kwargs: Request parameters to validate.

        Raises:
            ClientError: If validation fails.
        """
        if 'audio' not in kwargs and 'file' not in kwargs:
            raise ClientError("Speech request must include audio data or file")

    def requires_multipart(self) -> bool:
        """Check if this client requires multipart/form-data.

        Returns:
            False by default. Override in subclasses that need multipart.
        """
        return False
    
    @abstractmethod
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build speech request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The speech request payload.
        """
        pass
