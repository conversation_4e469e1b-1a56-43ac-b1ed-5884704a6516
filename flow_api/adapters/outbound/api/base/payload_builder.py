"""
Abstract payload builder module for flow_api.

This module provides abstract base classes for building API request payloads.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any

from flow_api.core.models import AIModel


class PayloadBuilder(ABC):
    """Abstract base class for payload builders.
    
    This class defines the interface for building API request payloads
    for different providers and capabilities.
    """
    
    def __init__(self, model: AIModel):
        """Initialize the payload builder.
        
        Args:
            model: The AI model to build payloads for.
        """
        self.model = model
    
    @abstractmethod
    def build_chat_payload(self, **kwargs) -> Dict[str, Any]:
        """Build a chat request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The chat request payload.
        """
        pass
    
    @abstractmethod
    def build_embedding_payload(self, **kwargs) -> Dict[str, Any]:
        """Build an embedding request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The embedding request payload.
        """
        pass
    
    def build_image_payload(self, **kwargs) -> Dict[str, Any]:
        """Build an image generation request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The image generation request payload.
        """
        # Default implementation - override if provider supports image generation
        raise NotImplementedError(f"Image generation not supported for {self.model.provider.value}")
    
    def build_speech_payload(self, **kwargs) -> Dict[str, Any]:
        """Build a speech-to-text request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The speech-to-text request payload.
        """
        # Default implementation - override if provider supports speech-to-text
        raise NotImplementedError(f"Speech-to-text not supported for {self.model.provider.value}")
