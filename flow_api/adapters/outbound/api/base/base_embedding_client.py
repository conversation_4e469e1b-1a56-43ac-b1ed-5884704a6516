"""
Base embedding client module for flow_api.

This module provides the base class for text embedding clients.
"""

from abc import abstractmethod
from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_api_client import BaseAPIClient
from flow_api.exceptions.client_error import ClientError


class BaseEmbeddingClient(BaseAPIClient):
    """Base class for text embedding clients.
    
    This class provides common functionality for embedding clients across different providers.
    """
    
    def validate_request(self, **kwargs) -> None:
        """Validate embedding request parameters.
        
        Args:
            **kwargs: Request parameters to validate.
            
        Raises:
            ClientError: If validation fails.
        """
        if 'input' not in kwargs and 'content' not in kwargs and 'inputText' not in kwargs:
            raise ClientError("Embedding request must include input, content, or inputText")
    
    @abstractmethod
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build embedding request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The embedding request payload.
        """
        pass
