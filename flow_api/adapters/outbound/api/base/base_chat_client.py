"""
Base chat client module for flow_api.

This module provides the base class for chat conversation clients.
"""

from abc import abstractmethod
from typing import Dict, Any

from flow_api.adapters.outbound.api.base.base_api_client import BaseAPIClient
from flow_api.exceptions.client_error import ClientError


class BaseChatClient(BaseAPIClient):
    """Base class for chat conversation clients.
    
    This class provides common functionality for chat clients across different providers.
    """
    
    def validate_request(self, **kwargs) -> None:
        """Validate chat request parameters.
        
        Args:
            **kwargs: Request parameters to validate.
            
        Raises:
            ClientError: If validation fails.
        """
        if 'messages' not in kwargs and 'contents' not in kwargs and 'prompt' not in kwargs:
            raise ClientError("Chat request must include messages, contents, or prompt")
    
    @abstractmethod
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build chat request payload.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The chat request payload.
        """
        pass
