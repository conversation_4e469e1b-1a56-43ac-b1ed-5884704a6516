"""
Base API client module for flow_api.

This module provides the base class for all API clients with common functionality.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
import logging

from flow_api.config import Config
from flow_api.adapters.outbound.auth.token_manager import TokenManager
from flow_api.core.models import AIModel

logger = logging.getLogger(__name__)


class BaseAPIClient(ABC):
    """Base class for all API clients.
    
    This class provides common functionality for API clients including
    authentication, headers, and configuration management.
    """
    
    def __init__(self, model: AIModel):
        """Initialize the base API client.
        
        Args:
            model: The AI model to use.
        """
        self.model = model
        self._config = Config()
        self._token_manager = TokenManager()
    
    def _get_base_headers(self, token: str) -> Dict[str, str]:
        """Get the base HTTP headers for API requests.
        
        Args:
            token: The authentication token.
            
        Returns:
            The base HTTP headers for API requests.
        """
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': self._config.flow_tenant,
            'Authorization': f'Bearer {token}',
            'FlowAgent': 'pyflow_api'
        }
    
    def _get_endpoint_url(self, endpoint_path: str) -> str:
        """Get the full endpoint URL.
        
        Args:
            endpoint_path: The endpoint path (e.g., '/openai/chat/completions').
            
        Returns:
            The full endpoint URL.
        """
        return f"{self._config.api_base_url}{endpoint_path}"
    
    @abstractmethod
    def get_endpoint_path(self) -> str:
        """Get the endpoint path for this client.
        
        Returns:
            The endpoint path.
        """
        pass
    
    @abstractmethod
    def validate_request(self, **kwargs) -> None:
        """Validate the request parameters for this client.
        
        Args:
            **kwargs: Request parameters to validate.
            
        Raises:
            ClientError: If validation fails.
        """
        pass
    
    @abstractmethod
    def build_request_payload(self, **kwargs) -> Dict[str, Any]:
        """Build the request payload for this client.
        
        Args:
            **kwargs: Request parameters.
            
        Returns:
            The request payload.
        """
        pass
    
    def process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process the API response.
        
        Args:
            response_data: The raw API response.
            
        Returns:
            The processed API response.
        """
        # Default implementation returns response as-is
        return response_data
