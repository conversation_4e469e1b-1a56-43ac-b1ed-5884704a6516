"""
Adapters module for flow_api.

This module contains the adapters for inbound and outbound communication,
including API clients, authentication, and specialized clients for different providers.
"""

# Import key components from inbound adapters
from flow_api.adapters.inbound.client import FlowAPIClient
from flow_api.adapters.inbound.capability_factory import CapabilityFactory
from flow_api.adapters.inbound.model_factory import ModelFactory

# Import key components from outbound adapters
from flow_api.adapters.outbound.api.client_factory import ClientFactory
from flow_api.adapters.outbound.api.specialized_client import SpecializedClient
from flow_api.adapters.outbound.auth.token_manager import TokenManager

__all__ = [
    "FlowAPIClient",
    "CapabilityFactory", 
    "ModelFactory",
    "ClientFactory",
    "SpecializedClient",
    "TokenManager"
]