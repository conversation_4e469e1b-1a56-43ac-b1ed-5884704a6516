#!/usr/bin/env python3
"""
Test runner script for flow_api.

This script provides various options for running tests with different configurations.
"""

import sys
import subprocess
import argparse
import os
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    if description:
        print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description or 'Command'} completed successfully!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description or 'Command'} failed with exit code {e.returncode}")
        return e.returncode
    except FileNotFoundError:
        print(f"\n❌ Command not found: {cmd[0]}")
        print("Make sure pytest is installed: pip install pytest")
        return 1


def main():
    parser = argparse.ArgumentParser(description="Run flow_api tests")
    parser.add_argument(
        "--type", 
        choices=["unit", "integration", "all"],
        default="all",
        help="Type of tests to run (default: all)"
    )
    parser.add_argument(
        "--coverage", 
        action="store_true",
        help="Run tests with coverage report"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="Run tests with verbose output"
    )
    parser.add_argument(
        "--fast", 
        action="store_true",
        help="Run tests in fast mode (skip slow tests)"
    )
    parser.add_argument(
        "--file", 
        type=str,
        help="Run specific test file"
    )
    parser.add_argument(
        "--function", 
        type=str,
        help="Run specific test function (use with --file)"
    )
    parser.add_argument(
        "--install-deps", 
        action="store_true",
        help="Install test dependencies before running tests"
    )
    
    args = parser.parse_args()
    
    # Change to project root directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # Install dependencies if requested
    if args.install_deps:
        deps_cmd = [
            sys.executable, "-m", "pip", "install", 
            "pytest", "pytest-mock", "pytest-cov", "pytest-timeout"
        ]
        if run_command(deps_cmd, "Installing test dependencies") != 0:
            return 1
    
    # Build pytest command
    cmd = [sys.executable, "-m", "pytest"]
    
    # Add coverage if requested
    if args.coverage:
        cmd.extend([
            "--cov=flow_api",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
    
    # Add verbosity
    if args.verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Add test type markers
    if args.type == "unit":
        cmd.extend(["-m", "unit"])
    elif args.type == "integration":
        cmd.extend(["-m", "integration"])
    
    # Skip slow tests if fast mode
    if args.fast:
        cmd.extend(["-m", "not slow"])
    
    # Add specific file or function
    if args.file:
        test_path = f"tests/{args.file}" if not args.file.startswith("tests/") else args.file
        if args.function:
            test_path += f"::{args.function}"
        cmd.append(test_path)
    else:
        cmd.append("tests/")
    
    # Run the tests
    description = f"Running {args.type} tests"
    if args.coverage:
        description += " with coverage"
    
    return run_command(cmd, description)


if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print(f"\n🎉 All tests passed!")
        print("\nNext steps:")
        print("1. Review test coverage report (if generated)")
        print("2. Check for any warnings in test output")
        print("3. Consider adding more tests for edge cases")
    else:
        print(f"\n💥 Tests failed with exit code {exit_code}")
        print("\nTroubleshooting:")
        print("1. Check the error messages above")
        print("2. Make sure all dependencies are installed")
        print("3. Verify environment variables are set correctly")
        print("4. Run specific failing tests with --verbose for more details")
    
    sys.exit(exit_code)
