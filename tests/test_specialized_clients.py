"""
Tests for specialized client classes.
"""

import pytest
from unittest.mock import Mock, patch

from flow_api.adapters.outbound.api.specialized_client import (
    SpecializedClient, ChatClient, EmbeddingClient, 
    ImageGenerationClient, SpeechClient
)
from flow_api.core.capabilities import Capability


class TestSpecializedClient:
    """Test the base SpecializedClient class."""
    
    def test_specialized_client_init(self, sample_ai_models):
        """Test SpecializedClient initialization."""
        model = sample_ai_models[0]  # gpt-4o-mini
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_factory.return_value = mock_provider_client
            
            client = SpecializedClient(model, Capability.CHAT_CONVERSATION)
            
            assert client.model == model
            assert client.capability == Capability.CHAT_CONVERSATION
            assert client._provider_client == mock_provider_client
            mock_factory.assert_called_once_with(model, Capability.CHAT_CONVERSATION)
    
    def test_specialized_client_init_without_capability(self, sample_ai_models):
        """Test SpecializedClient initialization without capability should raise error."""
        model = sample_ai_models[0]
        
        with pytest.raises(NotImplementedError, match="Subclasses must implement _get_default_capability"):
            SpecializedClient(model)
    
    def test_get_endpoint_path_delegation(self, sample_ai_models):
        """Test that get_endpoint_path delegates to provider client."""
        model = sample_ai_models[0]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_provider_client.get_endpoint_path.return_value = "/test/endpoint"
            mock_factory.return_value = mock_provider_client
            
            client = SpecializedClient(model, Capability.CHAT_CONVERSATION)
            result = client.get_endpoint_path()
            
            assert result == "/test/endpoint"
            mock_provider_client.get_endpoint_path.assert_called_once()
    
    def test_validate_request_delegation(self, sample_ai_models):
        """Test that validate_request delegates to provider client."""
        model = sample_ai_models[0]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_factory.return_value = mock_provider_client
            
            client = SpecializedClient(model, Capability.CHAT_CONVERSATION)
            client.validate_request(test_param="value")
            
            mock_provider_client.validate_request.assert_called_once_with(test_param="value")
    
    def test_build_request_payload_delegation(self, sample_ai_models):
        """Test that build_request_payload delegates to provider client."""
        model = sample_ai_models[0]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_provider_client.build_request_payload.return_value = {"test": "payload"}
            mock_factory.return_value = mock_provider_client
            
            client = SpecializedClient(model, Capability.CHAT_CONVERSATION)
            result = client.build_request_payload(test_param="value")
            
            assert result == {"test": "payload"}
            mock_provider_client.build_request_payload.assert_called_once_with(test_param="value")
    
    def test_process_response_delegation(self, sample_ai_models):
        """Test that process_response delegates to provider client."""
        model = sample_ai_models[0]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_provider_client.process_response.return_value = {"processed": "response"}
            mock_factory.return_value = mock_provider_client
            
            client = SpecializedClient(model, Capability.CHAT_CONVERSATION)
            result = client.process_response({"raw": "response"})
            
            assert result == {"processed": "response"}
            mock_provider_client.process_response.assert_called_once_with({"raw": "response"})
    
    def test_requires_multipart_with_provider_support(self, sample_ai_models):
        """Test requires_multipart when provider client supports it."""
        model = sample_ai_models[0]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_provider_client.requires_multipart.return_value = True
            mock_factory.return_value = mock_provider_client
            
            client = SpecializedClient(model, Capability.SPEECH_TO_TEXT)
            result = client.requires_multipart()
            
            assert result is True
            mock_provider_client.requires_multipart.assert_called_once()
    
    def test_requires_multipart_without_provider_support(self, sample_ai_models):
        """Test requires_multipart when provider client doesn't support it."""
        model = sample_ai_models[0]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            # Provider client doesn't have requires_multipart method
            del mock_provider_client.requires_multipart
            mock_factory.return_value = mock_provider_client
            
            client = SpecializedClient(model, Capability.CHAT_CONVERSATION)
            result = client.requires_multipart()
            
            assert result is False


class TestChatClient:
    """Test the ChatClient class."""
    
    def test_chat_client_init(self, sample_ai_models):
        """Test ChatClient initialization."""
        model = sample_ai_models[0]  # gpt-4o-mini with chat capability
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_factory.return_value = mock_provider_client
            
            client = ChatClient(model)
            
            assert client.model == model
            assert client.capability == Capability.CHAT_CONVERSATION
            mock_factory.assert_called_once_with(model, Capability.CHAT_CONVERSATION)
    
    def test_chat_client_default_capability(self, sample_ai_models):
        """Test ChatClient default capability."""
        model = sample_ai_models[0]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client'):
            client = ChatClient(model)
            assert client._get_default_capability() == Capability.CHAT_CONVERSATION


class TestEmbeddingClient:
    """Test the EmbeddingClient class."""
    
    def test_embedding_client_init(self, sample_ai_models):
        """Test EmbeddingClient initialization."""
        model = sample_ai_models[1]  # text-embedding-ada-002
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_factory.return_value = mock_provider_client
            
            client = EmbeddingClient(model)
            
            assert client.model == model
            assert client.capability == Capability.TEXT_EMBEDDING
            mock_factory.assert_called_once_with(model, Capability.TEXT_EMBEDDING)
    
    def test_embedding_client_default_capability(self, sample_ai_models):
        """Test EmbeddingClient default capability."""
        model = sample_ai_models[1]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client'):
            client = EmbeddingClient(model)
            assert client._get_default_capability() == Capability.TEXT_EMBEDDING


class TestImageGenerationClient:
    """Test the ImageGenerationClient class."""
    
    def test_image_generation_client_init(self, sample_ai_models):
        """Test ImageGenerationClient initialization."""
        model = sample_ai_models[2]  # dall-e-3
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_factory.return_value = mock_provider_client
            
            client = ImageGenerationClient(model)
            
            assert client.model == model
            assert client.capability == Capability.IMAGE_GENERATION
            mock_factory.assert_called_once_with(model, Capability.IMAGE_GENERATION)
    
    def test_image_generation_client_default_capability(self, sample_ai_models):
        """Test ImageGenerationClient default capability."""
        model = sample_ai_models[2]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client'):
            client = ImageGenerationClient(model)
            assert client._get_default_capability() == Capability.IMAGE_GENERATION


class TestSpeechClient:
    """Test the SpeechClient class."""
    
    def test_speech_client_init(self, sample_ai_models):
        """Test SpeechClient initialization."""
        model = sample_ai_models[3]  # whisper
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_factory.return_value = mock_provider_client
            
            client = SpeechClient(model)
            
            assert client.model == model
            assert client.capability == Capability.SPEECH_TO_TEXT
            mock_factory.assert_called_once_with(model, Capability.SPEECH_TO_TEXT)
    
    def test_speech_client_default_capability(self, sample_ai_models):
        """Test SpeechClient default capability."""
        model = sample_ai_models[3]
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client'):
            client = SpeechClient(model)
            assert client._get_default_capability() == Capability.SPEECH_TO_TEXT
    
    def test_speech_client_requires_multipart(self, sample_ai_models):
        """Test that SpeechClient properly handles multipart requirements."""
        model = sample_ai_models[3]  # whisper
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_provider_client.requires_multipart.return_value = True
            mock_factory.return_value = mock_provider_client
            
            client = SpeechClient(model)
            result = client.requires_multipart()
            
            assert result is True


class TestClientFactory:
    """Test client creation through factory."""
    
    def test_factory_creates_correct_client_type(self, sample_ai_models):
        """Test that factory creates the correct client type for each capability."""
        model = sample_ai_models[0]  # gpt-4o-mini
        
        with patch('flow_api.adapters.outbound.api.client_factory.ClientFactory.create_client') as mock_factory:
            mock_provider_client = Mock()
            mock_factory.return_value = mock_provider_client
            
            # Test different client types
            chat_client = ChatClient(model)
            embedding_client = EmbeddingClient(model)
            image_client = ImageGenerationClient(model)
            speech_client = SpeechClient(model)
            
            # Verify factory was called with correct capabilities
            expected_calls = [
                (model, Capability.CHAT_CONVERSATION),
                (model, Capability.TEXT_EMBEDDING),
                (model, Capability.IMAGE_GENERATION),
                (model, Capability.SPEECH_TO_TEXT)
            ]
            actual_calls = [call.args for call in mock_factory.call_args_list]
            assert actual_calls == expected_calls
