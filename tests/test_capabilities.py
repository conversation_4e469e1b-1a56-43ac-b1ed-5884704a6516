"""
Tests for the Capability enum and related functionality.
"""

import pytest
from flow_api.core.capabilities import Capability


class TestCapability:
    """Test the Capability enum."""
    
    def test_capability_values(self):
        """Test that all expected capability values exist."""
        expected_capabilities = {
            "streaming",
            "system-instruction", 
            "chat-conversation",
            "image-recognition",
            "text-embedding",
            "image-generation",
            "speech-to-text"
        }
        
        actual_capabilities = {cap.value for cap in Capability}
        assert actual_capabilities == expected_capabilities
    
    def test_from_str_valid(self):
        """Test converting valid strings to Capability enum."""
        test_cases = [
            ("streaming", Capability.STREAMING),
            ("system-instruction", Capability.SYSTEM_INSTRUCTION),
            ("chat-conversation", Capability.CHAT_CONVERSATION),
            ("image-recognition", Capability.IMAGE_RECOGNITION),
            ("text-embedding", Capability.TEXT_EMBEDDING),
            ("image-generation", Capability.IMAGE_GENERATION),
            ("speech-to-text", Capability.SPEECH_TO_TEXT)
        ]
        
        for string_val, expected_enum in test_cases:
            result = Capability.from_str(string_val)
            assert result == expected_enum
            assert result.value == string_val
    
    def test_from_str_invalid(self):
        """Test converting invalid strings raises ValueError."""
        invalid_capabilities = [
            "invalid-capability",
            "chat",
            "embedding",
            "image",
            "speech",
            "",
            "CHAT_CONVERSATION",  # Wrong case
            "chat_conversation"   # Wrong separator
        ]
        
        for invalid_cap in invalid_capabilities:
            with pytest.raises(ValueError, match=f"Invalid capability: {invalid_cap}"):
                Capability.from_str(invalid_cap)
    
    def test_list_from_strs_valid(self):
        """Test converting list of valid strings to Capability enums."""
        input_strings = [
            "chat-conversation",
            "text-embedding", 
            "streaming"
        ]
        
        expected_enums = [
            Capability.CHAT_CONVERSATION,
            Capability.TEXT_EMBEDDING,
            Capability.STREAMING
        ]
        
        result = Capability.list_from_strs(input_strings)
        assert result == expected_enums
    
    def test_list_from_strs_empty(self):
        """Test converting empty list returns empty list."""
        result = Capability.list_from_strs([])
        assert result == []
    
    def test_list_from_strs_with_invalid(self):
        """Test converting list with invalid string raises ValueError."""
        input_strings = [
            "chat-conversation",
            "invalid-capability",
            "text-embedding"
        ]
        
        with pytest.raises(ValueError, match="Invalid capability: invalid-capability"):
            Capability.list_from_strs(input_strings)
    
    def test_capability_enum_properties(self):
        """Test that Capability enum has expected properties."""
        # Test that it's a string enum
        assert isinstance(Capability.CHAT_CONVERSATION, str)
        assert Capability.CHAT_CONVERSATION == "chat-conversation"
        
        # Test enum iteration
        all_capabilities = list(Capability)
        assert len(all_capabilities) == 7
        
        # Test enum membership
        assert Capability.STREAMING in Capability
        assert "not-a-capability" not in [cap.value for cap in Capability]
    
    def test_capability_comparison(self):
        """Test capability comparison operations."""
        # Test equality
        assert Capability.CHAT_CONVERSATION == Capability.CHAT_CONVERSATION
        assert Capability.CHAT_CONVERSATION != Capability.TEXT_EMBEDDING
        
        # Test string comparison
        assert Capability.CHAT_CONVERSATION == "chat-conversation"
        assert Capability.CHAT_CONVERSATION != "text-embedding"
        
        # Test in collections
        capabilities_set = {Capability.CHAT_CONVERSATION, Capability.TEXT_EMBEDDING}
        assert Capability.CHAT_CONVERSATION in capabilities_set
        assert Capability.IMAGE_GENERATION not in capabilities_set
    
    def test_capability_serialization(self):
        """Test that capabilities can be serialized/deserialized."""
        import json
        
        # Test single capability
        cap = Capability.CHAT_CONVERSATION
        serialized = json.dumps(cap.value)
        deserialized = json.loads(serialized)
        assert deserialized == cap.value
        assert Capability.from_str(deserialized) == cap
        
        # Test list of capabilities
        caps = [Capability.CHAT_CONVERSATION, Capability.TEXT_EMBEDDING]
        serialized = json.dumps([cap.value for cap in caps])
        deserialized = json.loads(serialized)
        restored_caps = Capability.list_from_strs(deserialized)
        assert restored_caps == caps
