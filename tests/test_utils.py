"""
Tests for utility functions.
"""

import pytest
import logging
from unittest.mock import Mock, patch
from io import StringIO

from flow_api.utils.validation import validate_messages
from flow_api.utils.logging import get_logger, log_and_raise
from flow_api.exceptions.client_error import ClientError


class TestValidation:
    """Test validation utility functions."""
    
    def test_validate_messages_string_input(self):
        """Test validating string input."""
        result = validate_messages("Hello, world!")
        
        expected = [{"role": "user", "content": "Hello, world!"}]
        assert result == expected
    
    def test_validate_messages_none_input(self):
        """Test validating None input."""
        result = validate_messages(None)
        
        assert result == []
    
    def test_validate_messages_empty_string(self):
        """Test validating empty string input."""
        result = validate_messages("")
        
        expected = [{"role": "user", "content": ""}]
        assert result == expected
    
    def test_validate_messages_list_input_valid(self):
        """Test validating valid list input."""
        input_messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"},
            {"role": "user", "content": "How are you?"}
        ]
        
        result = validate_messages(input_messages)
        assert result == input_messages
    
    def test_validate_messages_list_input_with_content_array(self):
        """Test validating list input with content arrays (for multimodal)."""
        input_messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "What's in this image?"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            }
        ]
        
        result = validate_messages(input_messages)
        assert result == input_messages
    
    def test_validate_messages_list_input_invalid_structure(self):
        """Test validating list input with invalid structure."""
        input_messages = [
            {"invalid": "structure"},  # Missing content
            {"role": "user"}  # Missing content
        ]

        with pytest.raises(ValueError, match="Message is missing 'content' field"):
            validate_messages(input_messages)
    
    def test_validate_messages_list_input_invalid_role(self):
        """Test validating list input with invalid role."""
        input_messages = [
            {"role": "invalid_role", "content": "Hello"}
        ]

        # The current implementation doesn't validate roles, it passes them through
        result = validate_messages(input_messages)
        assert result == input_messages
    
    def test_validate_messages_list_input_missing_content(self):
        """Test validating list input with missing content."""
        input_messages = [
            {"role": "user"}  # Missing content
        ]

        with pytest.raises(ValueError, match="Message is missing 'content' field"):
            validate_messages(input_messages)
    
    def test_validate_messages_invalid_type(self):
        """Test validating invalid input type."""
        with pytest.raises(ValueError, match="Expected user_prompt to be a string or list"):
            validate_messages(123)  # type: ignore[arg-type]

        with pytest.raises(ValueError, match="Expected user_prompt to be a string or list"):
            validate_messages({"not": "a list"})  # type: ignore[arg-type]
    
    def test_validate_messages_empty_list(self):
        """Test validating empty list."""
        result = validate_messages([])
        assert result == []
    
    def test_validate_messages_mixed_content_types(self):
        """Test validating messages with mixed content types."""
        input_messages = [
            {"role": "user", "content": "Simple text"},
            {
                "role": "user", 
                "content": [
                    {"type": "text", "text": "Complex content"}
                ]
            }
        ]
        
        result = validate_messages(input_messages)
        assert result == input_messages


class TestLogging:
    """Test logging utility functions."""
    
    def test_get_logger_returns_logger(self):
        """Test that get_logger returns a logger instance."""
        logger = get_logger("test_module")
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == "test_module"
    
    def test_get_logger_same_name_returns_same_instance(self):
        """Test that get_logger returns the same instance for the same name."""
        logger1 = get_logger("test_module")
        logger2 = get_logger("test_module")
        
        assert logger1 is logger2
    
    def test_get_logger_different_names_return_different_instances(self):
        """Test that get_logger returns different instances for different names."""
        logger1 = get_logger("test_module1")
        logger2 = get_logger("test_module2")
        
        assert logger1 is not logger2
        assert logger1.name == "test_module1"
        assert logger2.name == "test_module2"
    
    def test_log_and_raise_with_debug_level(self):
        """Test log_and_raise with debug level."""
        logger = Mock()
        
        with pytest.raises(ClientError, match="Test error message"):
            log_and_raise(logger, "Test error message", ClientError, "debug")
        
        logger.debug.assert_called_once_with("Test error message")
    
    def test_log_and_raise_with_info_level(self):
        """Test log_and_raise with info level."""
        logger = Mock()
        
        with pytest.raises(ClientError, match="Test error message"):
            log_and_raise(logger, "Test error message", ClientError, "info")
        
        logger.info.assert_called_once_with("Test error message")
    
    def test_log_and_raise_with_warning_level(self):
        """Test log_and_raise with warning level."""
        logger = Mock()
        
        with pytest.raises(ClientError, match="Test error message"):
            log_and_raise(logger, "Test error message", ClientError, "warning")
        
        logger.warning.assert_called_once_with("Test error message")
    
    def test_log_and_raise_with_error_level(self):
        """Test log_and_raise with error level."""
        logger = Mock()
        
        with pytest.raises(ClientError, match="Test error message"):
            log_and_raise(logger, "Test error message", ClientError, "error")
        
        logger.error.assert_called_once_with("Test error message")
    
    def test_log_and_raise_with_critical_level(self):
        """Test log_and_raise with critical level."""
        logger = Mock()
        
        with pytest.raises(ClientError, match="Test error message"):
            log_and_raise(logger, "Test error message", ClientError, "critical")
        
        logger.critical.assert_called_once_with("Test error message")
    
    def test_log_and_raise_with_invalid_level(self):
        """Test log_and_raise with invalid level calls the level as-is."""
        logger = Mock()

        with pytest.raises(ClientError, match="Test error message"):
            log_and_raise(logger, "Test error message", ClientError, "invalid_level")

        # The implementation calls getattr(logger, log_level.lower()) which would be logger.invalid_level
        logger.invalid_level.assert_called_once_with("Test error message")
    
    def test_log_and_raise_with_different_exception_types(self):
        """Test log_and_raise with different exception types."""
        logger = Mock()
        
        # Test with ValueError
        with pytest.raises(ValueError, match="Value error message"):
            log_and_raise(logger, "Value error message", ValueError, "error")
        
        # Test with RuntimeError
        with pytest.raises(RuntimeError, match="Runtime error message"):
            log_and_raise(logger, "Runtime error message", RuntimeError, "error")
        
        # Test with custom exception
        class CustomError(Exception):
            pass
        
        with pytest.raises(CustomError, match="Custom error message"):
            log_and_raise(logger, "Custom error message", CustomError, "error")
    
    def test_log_and_raise_preserves_exception_args(self):
        """Test that log_and_raise preserves exception arguments."""
        logger = Mock()
        
        with pytest.raises(ClientError) as exc_info:
            log_and_raise(logger, "Test message", ClientError, "error")
        
        assert exc_info.value.args == ("Test message",)
        assert str(exc_info.value) == "Test message"
    
    def test_log_and_raise_with_empty_message(self):
        """Test log_and_raise with empty message."""
        logger = Mock()
        
        with pytest.raises(ClientError, match="^$"):  # Empty string
            log_and_raise(logger, "", ClientError, "error")
        
        logger.error.assert_called_once_with("")
    
    def test_log_and_raise_with_formatted_message(self):
        """Test log_and_raise with formatted message."""
        logger = Mock()
        
        model_name = "gpt-4o-mini"
        capability = "text-embedding"
        message = f"Model '{model_name}' does not support '{capability}'"
        
        with pytest.raises(ClientError) as exc_info:
            log_and_raise(logger, message, ClientError, "error")
        
        expected_message = "Model 'gpt-4o-mini' does not support 'text-embedding'"
        assert str(exc_info.value) == expected_message
        logger.error.assert_called_once_with(expected_message)
    
    @patch('flow_api.utils.logging.logging.getLogger')
    def test_get_logger_configuration(self, mock_get_logger):
        """Test that get_logger properly configures the logger."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        result = get_logger("test_module")
        
        assert result == mock_logger
        mock_get_logger.assert_called_once_with("test_module")
    
    def test_logging_integration(self):
        """Test logging integration with actual logger."""
        # Create a string buffer to capture log output
        log_buffer = StringIO()
        
        # Create a logger with a stream handler
        logger = logging.getLogger("test_integration")
        logger.setLevel(logging.DEBUG)
        
        # Remove any existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Add our test handler
        handler = logging.StreamHandler(log_buffer)
        handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(levelname)s: %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        # Test logging at different levels
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")
        
        # Check the output
        log_output = log_buffer.getvalue()
        assert "DEBUG: Debug message" in log_output
        assert "INFO: Info message" in log_output
        assert "WARNING: Warning message" in log_output
        assert "ERROR: Error message" in log_output
        assert "CRITICAL: Critical message" in log_output
