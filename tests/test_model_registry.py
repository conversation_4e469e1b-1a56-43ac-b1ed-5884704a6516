"""
Tests for the ModelRegistry class.
"""

import pytest
from unittest.mock import patch

from flow_api.core.model_registry import ModelRegistry
from flow_api.core.models import AIModel, ModelProvider
from flow_api.core.capabilities import Capability
from flow_api.exceptions.client_error import ClientError
from flow_api.adapters.outbound.auth.token_manager import TokenManager


class TestModelRegistry:
    """Test the ModelRegistry class."""
    
    def test_singleton_pattern(self, mock_env_vars):
        """Test that ModelRegistry follows singleton pattern."""
        with patch.object(ModelRegistry, '_load_models'):
            registry1 = ModelRegistry()
            registry2 = ModelRegistry()
            
            assert registry1 is registry2
    
    def test_load_models_from_cache(self, mock_env_vars, mock_models_data, mock_auth_and_models):
        """Test loading models from cache."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached, \
             patch.object(TokenManager, 'refresh_models') as mock_refresh:

            # The cache returns the models data directly, not wrapped in 'models'
            cache_data = mock_models_data
            mock_get_cached.return_value = cache_data

            registry = ModelRegistry()

            # Verify models were loaded
            models = registry.get_all_models()
            assert len(models) > 0
            
            # Check specific models
            model_names = [model.name for model in models]
            assert "gpt-4o-mini" in model_names
            assert "text-embedding-ada-002" in model_names
            assert "dall-e-3" in model_names
            assert "whisper" in model_names
    
    def test_load_models_from_api(self, mock_env_vars, mock_models_data, mock_auth_and_models):
        """Test loading models from API when cache is empty."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached, \
             patch.object(TokenManager, 'refresh_models') as mock_refresh, \
             patch('flow_api.utils.cache.LocalCache.cache_models') as mock_cache_models:

            mock_get_cached.return_value = None
            mock_refresh.return_value = mock_models_data

            registry = ModelRegistry()

            # Verify API was called
            mock_refresh.assert_called_once()
            # Verify models were cached
            mock_cache_models.assert_called()
            
            # Verify models were loaded
            models = registry.get_all_models()
            assert len(models) > 0
    
    def test_load_models_api_failure(self, mock_env_vars):
        """Test handling API failure when loading models."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached, \
             patch.object(TokenManager, 'refresh_models') as mock_refresh:

            mock_get_cached.return_value = None
            mock_refresh.side_effect = Exception("API Error")

            with pytest.raises(ClientError, match="Failed to load models"):
                ModelRegistry()
    
    def test_infer_capabilities_from_name(self, mock_env_vars):
        """Test capability inference from model names."""
        with patch.object(ModelRegistry, '_load_models'):
            registry = ModelRegistry()
            
            # Test image generation models
            assert registry._infer_capabilities_from_name("dall-e-3") == [Capability.IMAGE_GENERATION]
            assert registry._infer_capabilities_from_name("dalle-2") == [Capability.IMAGE_GENERATION]
            assert registry._infer_capabilities_from_name("image-gen-model") == [Capability.IMAGE_GENERATION]
            
            # Test speech-to-text models
            assert registry._infer_capabilities_from_name("whisper") == [Capability.SPEECH_TO_TEXT]
            assert registry._infer_capabilities_from_name("speech-model") == [Capability.SPEECH_TO_TEXT]
            assert registry._infer_capabilities_from_name("transcrib-model") == [Capability.SPEECH_TO_TEXT]  # Uses 'transcrib' not 'transcription'
            
            # Test text embedding models
            assert registry._infer_capabilities_from_name("text-embedding-ada-002") == [Capability.TEXT_EMBEDDING]
            assert registry._infer_capabilities_from_name("embed-model") == [Capability.TEXT_EMBEDDING]
            assert registry._infer_capabilities_from_name("vector-model") == [Capability.TEXT_EMBEDDING]
            
            # Test default (chat conversation)
            assert registry._infer_capabilities_from_name("gpt-4") == [Capability.CHAT_CONVERSATION]
            assert registry._infer_capabilities_from_name("unknown-model") == [Capability.CHAT_CONVERSATION]
    
    def test_sanitize_models_data(self, mock_env_vars, mock_models_data):
        """Test sanitization of models data with capability inference."""
        with patch.object(ModelRegistry, '_load_models'):
            registry = ModelRegistry()
            
            # Create test data with empty capabilities
            test_data = {
                "supportedModels": {
                    "azureOpenai": [
                        {
                            "name": "dall-e-3",
                            "capabilities": []
                        },
                        {
                            "name": "whisper",
                            "capabilities": []
                        },
                        {
                            "name": "gpt-4",
                            "capabilities": ["chat-conversation"]
                        }
                    ]
                }
            }
            
            result = registry._sanitize_models_data(test_data)
            
            # Check that capabilities were inferred
            models = result["supportedModels"]["azureOpenai"]
            dalle_model = next(m for m in models if m["name"] == "dall-e-3")
            whisper_model = next(m for m in models if m["name"] == "whisper")
            gpt_model = next(m for m in models if m["name"] == "gpt-4")
            
            assert dalle_model["capabilities"] == ["image-generation"]
            assert whisper_model["capabilities"] == ["speech-to-text"]
            assert gpt_model["capabilities"] == ["chat-conversation"]  # Should remain unchanged
    
    def test_get_model_existing(self, mock_env_vars, mock_models_data):
        """Test getting an existing model by name."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached:
            cache_data = mock_models_data
            mock_get_cached.return_value = cache_data

            registry = ModelRegistry()
            model = registry.get_model("gpt-4o-mini")

            assert model is not None
            assert model.name == "gpt-4o-mini"
            assert model.provider == ModelProvider.AZURE_OPENAI
    
    def test_get_model_nonexistent(self, mock_env_vars, mock_models_data):
        """Test getting a non-existent model returns None."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached:
            mock_get_cached.return_value = mock_models_data
            registry = ModelRegistry()
            model = registry.get_model("nonexistent-model")
            assert model is None
    
    def test_get_models_by_capability(self, mock_env_vars, mock_models_data):
        """Test getting models by specific capability."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached:
            cache_data = mock_models_data
            mock_get_cached.return_value = cache_data

            registry = ModelRegistry()

            # Test chat conversation models
            chat_models = registry.get_models_by_capability(Capability.CHAT_CONVERSATION)
            chat_model_names = [model.name for model in chat_models]
            assert "gpt-4o-mini" in chat_model_names

            # Test text embedding models
            embedding_models = registry.get_models_by_capability(Capability.TEXT_EMBEDDING)
            embedding_model_names = [model.name for model in embedding_models]
            assert "text-embedding-ada-002" in embedding_model_names
            
            # Test capability that no model has
            nonexistent_models = registry.get_models_by_capability(Capability.SYSTEM_INSTRUCTION)
            # This might be empty or contain models depending on test data
    
    def test_get_models_by_capabilities_multiple(self, mock_env_vars, mock_models_data):
        """Test getting models by multiple capabilities."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached:
            mock_get_cached.return_value = mock_models_data
            registry = ModelRegistry()
            # Test models with multiple capabilities
            result = registry.get_models_by_capabilities([Capability.CHAT_CONVERSATION, Capability.IMAGE_RECOGNITION])

            # Assertions can be made here
    
    def test_get_available_capabilities(self, mock_env_vars, mock_models_data):
        """Test getting all available capabilities."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached:
            cache_data = mock_models_data
            mock_get_cached.return_value = cache_data
            
            registry = ModelRegistry()
            capabilities = registry.get_available_capabilities()
            
            assert isinstance(capabilities, set)
            assert len(capabilities) > 0
            
            # Should contain at least some expected capabilities
            expected_capabilities = {
                Capability.CHAT_CONVERSATION,
                Capability.TEXT_EMBEDDING,
                Capability.STREAMING
            }
            assert expected_capabilities.issubset(capabilities)
    
    def test_refresh_models(self, mock_env_vars, mock_models_data, mock_auth_and_models):
        """Test refreshing models from API."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached, \
             patch.object(TokenManager, 'refresh_models') as mock_refresh, \
             patch('flow_api.utils.cache.LocalCache.cache_models') as mock_cache_models:

            cache_data = mock_models_data
            mock_get_cached.return_value = cache_data
            mock_refresh.return_value = cache_data

            registry = ModelRegistry()

            # Clear the refresh call from initialization
            mock_refresh.reset_mock()
            mock_cache_models.reset_mock()

            # Test refresh
            registry.refresh_models()

            mock_refresh.assert_called_once()
            mock_cache_models.assert_called()
    
    def test_refresh_models_failure(self, mock_env_vars, mock_models_data):
        """Test handling refresh failure."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached, \
             patch.object(TokenManager, 'refresh_models') as mock_refresh:

            cache_data = mock_models_data
            mock_get_cached.return_value = cache_data
            mock_refresh.side_effect = Exception("API Error")

            registry = ModelRegistry()

            with pytest.raises(ClientError, match="Failed to refresh models"):
                registry.refresh_models()
    
    def test_get_raw_models_data(self, mock_env_vars, mock_models_data):
        """Test getting raw models data."""
        with patch.object(TokenManager, 'get_cached_models') as mock_get_cached:
            cache_data = mock_models_data
            mock_get_cached.return_value = cache_data

            registry = ModelRegistry()
            raw_data = registry.get_raw_models_data()

            assert raw_data == cache_data
