"""
Tests for exception classes.
"""

import pytest
from flow_api.exceptions.client_error import ClientError
from flow_api.utils.logging import get_logger, log_and_raise


logger = get_logger(__name__)

class TestClientError:
    """Test the ClientError exception class."""
    
    def test_client_error_creation(self):
        """Test creating a ClientError with message."""
        error = ClientError("Test error message")
        
        assert str(error) == "Test error message"
        assert error.args == ("Test error message",)
    
    def test_client_error_inheritance(self):
        """Test that ClientError inherits from Exception."""
        error = ClientError("Test error")
        
        assert isinstance(error, Exception)
        assert isinstance(error, ClientError)
    
    def test_client_error_empty_message(self):
        """Test creating a ClientError with empty message."""
        error = ClientError("")
        
        assert str(error) == ""
        assert error.args == ("",)
    
    def test_client_error_none_message(self):
        """Test creating a ClientError with None message should handle gracefully."""
        # The current implementation doesn't handle None messages well
        # This test verifies the behavior but we might want to improve the implementation
        with pytest.raises(TypeError):
            ClientError(None) # type: ignore
    
    def test_client_error_with_cause(self):
        """Test creating a ClientError with underlying cause."""
        original_error = ValueError("Original error")

        try:
            raise original_error
        except ValueError as e:
            try:
                raise ClientError("Wrapped error") from e
            except ClientError as client_error:
                assert str(client_error) == "Wrapped error"
                assert client_error.__cause__ is original_error
    
    def test_client_error_raising(self):
        """Test raising and catching ClientError."""
        with pytest.raises(ClientError) as exc_info:
            raise ClientError("Test error for raising")
        
        assert str(exc_info.value) == "Test error for raising"
        assert exc_info.type is ClientError
    
    def test_client_error_in_exception_hierarchy(self):
        """Test that ClientError can be caught as Exception."""
        with pytest.raises(Exception) as exc_info:
            raise ClientError("Test error")
        
        assert isinstance(exc_info.value, ClientError)
        assert str(exc_info.value) == "Test error"
    
    def test_client_error_with_additional_args(self):
        """Test creating ClientError with status code and activity ID."""
        error = ClientError("Main message", status_code=400, activity_id="test-activity-123")

        expected_message = "Main message | Status code: 400 | Activity ID: test-activity-123"
        assert str(error) == expected_message
        assert error.message == "Main message"
        assert error.status_code == 400
        assert error.activity_id == "test-activity-123"
    
    def test_client_error_repr(self):
        """Test string representation of ClientError."""
        error = ClientError("Test error message")
        
        # The repr should include the class name and message
        repr_str = repr(error)
        assert "ClientError" in repr_str
        assert "Test error message" in repr_str
    
    def test_client_error_equality(self):
        """Test equality comparison of ClientError instances."""
        error1 = ClientError("Same message")
        error2 = ClientError("Same message")
        error3 = ClientError("Different message")
        
        # Note: Exception instances are not equal even with same message
        # This is standard Python behavior
        assert error1 != error2
        assert error1 != error3
        assert error2 != error3
        
        # But their string representations should be equal
        assert str(error1) == str(error2)
        assert str(error1) != str(error3)
    
    def test_client_error_with_format_string(self):
        """Test ClientError with formatted message."""
        model_name = "gpt-4o-mini"
        capability = "text-embedding"
        
        error = ClientError(f"Model '{model_name}' does not support capability '{capability}'")
        
        expected_message = "Model 'gpt-4o-mini' does not support capability 'text-embedding'"
        assert str(error) == expected_message
    
    def test_client_error_traceback_preservation(self):
        """Test that ClientError preserves traceback information."""
        def inner_function():
            raise ClientError("Inner error")
        
        def outer_function():
            try:
                inner_function()
            except ClientError:
                raise ClientError("Outer error") from None
        
        with pytest.raises(ClientError) as exc_info:
            outer_function()
        
        # The traceback should include both functions
        tb = exc_info.tb
        assert tb is not None
        
        # Should have frames from both outer_function and inner_function
        frame_names = []
        while tb:
            frame_names.append(tb.tb_frame.f_code.co_name)
            tb = tb.tb_next
        
        assert "outer_function" in frame_names
    
    def test_client_error_chaining(self):
        """Test exception chaining with ClientError."""
        def function_that_fails():
            log_and_raise(logger, "Original failure", ValueError, "debug")

        def wrapper_function():
            try:
                function_that_fails()
            except ValueError as e:
                try:
                    raise ClientError("Wrapped failure") from e
                except ClientError:
                    raise

        with pytest.raises(ClientError) as exc_info:
            wrapper_function()

        client_error = exc_info.value
        assert str(client_error) == "Wrapped failure"
        assert isinstance(client_error.__cause__, ValueError)
        assert str(client_error.__cause__) == "Original failure"
    
    def test_client_error_suppression(self):
        """Test exception suppression with ClientError."""
        def function_with_suppressed_exception():
            try:
                log_and_raise(logger, "Original error", ValueError, "debug")
            except ValueError:
                try:
                    raise ClientError("New error") from None
                except ClientError:
                    raise

        with pytest.raises(ClientError) as exc_info:
            function_with_suppressed_exception()

        client_error = exc_info.value
        assert str(client_error) == "New error"
        assert client_error.__cause__ is None
        assert client_error.__suppress_context__ is True
