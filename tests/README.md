# Flow API Tests

This directory contains comprehensive unit and integration tests for the Flow API Python library.

## Test Structure

```
tests/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Pytest configuration and fixtures
├── test_client.py              # Main FlowAPIClient class tests
├── test_capabilities.py        # Capability enum tests
├── test_models.py              # AIModel and ModelProvider tests
├── test_model_registry.py      # ModelRegistry tests
├── test_config.py              # Configuration tests
├── test_specialized_clients.py # Specialized client tests
├── test_client_capabilities.py # FlowAPIClient capability method tests
├── test_exceptions.py          # Exception class tests
├── test_utils.py               # Utility function tests
├── test_integration.py         # Integration tests
└── README.md                   # This file
```

## Running Tests

### Prerequisites

Install test dependencies:

```bash
pip install pytest pytest-mock pytest-cov pytest-timeout
```

### Quick Start

Run all tests:

```bash
python tests/run_tests.py
```

Run with coverage:

```bash
python tests/run_tests.py --coverage
```

### Using pytest directly

Run all tests:

```bash
pytest tests/
```

Run specific test file:

```bash
pytest tests/test_client.py
```

Run specific test function:

```bash
pytest tests/test_client.py::TestClientInitialization::test_client_init_without_default_model
```

Run with coverage:

```bash
pytest --cov=flow_api --cov-report=html tests/
```

### Test Categories

Run only unit tests:

```bash
pytest -m unit tests/
```

Run only integration tests:

```bash
pytest -m integration tests/
```

Skip slow tests:

```bash
pytest -m "not slow" tests/
```

## Test Configuration

### Environment Variables

Tests use mocked environment variables by default. The following variables are mocked in `conftest.py`:

- `FLOW_API_CLIENT_ID`: test_client_id
- `FLOW_API_CLIENT_SECRET`: test_client_secret
- `FLOW_API_APP_TO_ACCESS`: test_app
- `FLOW_API_TENANT`: test_tenant
- `FLOW_API_BASE_URL`: https://test-api.example.com
- `FLOW_API_CONSOLE_LOG_LEVEL`: OFF
- `FLOW_API_FILE_LOG_LEVEL`: OFF

### Fixtures

Key fixtures available in `conftest.py`:

- `mock_env_vars`: Mocked environment variables
- `mock_models_data`: Sample models data for testing
- `sample_ai_models`: Sample AIModel instances
- `mock_api_responses`: Mock API responses for different endpoints
- `mock_token_response`: Mock authentication token response
- `mock_requests_session`: Mock requests session
- `mock_file_operations`: Mock file operations for cache/token management

## Test Coverage

### Core Components

- ✅ **FlowAPIClient**: Main client initialization, model selection, requests
- ✅ **Capabilities**: Capability enum and validation
- ✅ **Models**: AIModel and ModelProvider classes
- ✅ **ModelRegistry**: Model loading, caching, and querying
- ✅ **Config**: Configuration management and validation
- ✅ **SpecializedClients**: Chat, embedding, image, speech clients
- ✅ **Exceptions**: Error handling and custom exceptions
- ✅ **Utils**: Validation and logging utilities

### Functionality Tests

- ✅ **Text Generation**: Chat conversations with system prompts
- ✅ **Text Embedding**: Vector generation from text
- ✅ **Image Generation**: Image creation from prompts
- ✅ **Speech-to-Text**: Audio transcription
- ✅ **Streaming**: Real-time response streaming
- ✅ **Model Selection**: Capability-based and explicit selection

### Integration Tests

- ✅ **End-to-End Workflows**: Complete user workflows
- ✅ **Error Handling**: Network, HTTP, and API errors
- ✅ **Model Compatibility**: Cross-provider functionality

## Writing New Tests

### Test Naming Convention

- Test files: `test_<module_name>.py`
- Test classes: `Test<ClassName>`
- Test methods: `test_<functionality>_<scenario>`

### Example Test Structure

```python
class TestNewFeature:
    """Test the new feature functionality."""

    def test_feature_success_case(self, mock_env_vars):
        """Test successful feature operation."""
        # Arrange
        # Act
        # Assert

    def test_feature_error_case(self, mock_env_vars):
        """Test feature error handling."""
        # Arrange
        # Act & Assert
        with pytest.raises(ClientError, match="Expected error"):
            # Code that should raise error
```

### Best Practices

1. **Use descriptive test names** that explain what is being tested
2. **Follow AAA pattern**: Arrange, Act, Assert
3. **Mock external dependencies** (API calls, file operations, etc.)
4. **Test both success and error cases**
5. **Use fixtures** for common setup
6. **Keep tests independent** - each test should be able to run in isolation
7. **Test edge cases** and boundary conditions

### Mocking Guidelines

```python
# Mock external API calls
with patch('requests.post') as mock_post:
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {"test": "data"}
    mock_post.return_value = mock_response

# Mock internal components
with patch('flow_api.core.model_registry.ModelRegistry._load_models'):
    # Test code here

# Use fixtures for common mocks
def test_something(self, mock_env_vars, mock_models_data):
    # Test code with mocked environment and models
```

## Continuous Integration

These tests are designed to run in CI/CD environments. Key considerations:

- **No external dependencies**: All API calls and file operations are mocked
- **Fast execution**: Unit tests complete in seconds
- **Deterministic**: Tests produce consistent results
- **Comprehensive coverage**: All major code paths are tested

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure the project root is in PYTHONPATH
2. **Missing fixtures**: Check that `conftest.py` is properly configured
3. **Mock failures**: Verify that external dependencies are properly mocked
4. **Environment issues**: Ensure test environment variables are set

### Debug Tips

```bash
# Run with verbose output
pytest -v tests/

# Run specific test with output
pytest -s tests/test_client.py::test_specific_function

# Run with pdb debugger
pytest --pdb tests/test_client.py

# Show test coverage
pytest --cov=flow_api --cov-report=term-missing tests/
```

## Contributing

When adding new features:

1. Write tests first (TDD approach)
2. Ensure all existing tests pass
3. Add integration tests for new workflows
4. Update this README if adding new test categories
5. Maintain test coverage above 90%
