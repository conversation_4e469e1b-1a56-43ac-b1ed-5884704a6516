"""
Tests for the AIModel and ModelProvider classes.
"""

import pytest
from flow_api.core.models import AIModel, ModelProvider
from flow_api.core.capabilities import Capability


class TestModelProvider:
    """Test the ModelProvider enum."""
    
    def test_model_provider_values(self):
        """Test that all expected provider values exist."""
        expected_providers = {
            "azure-openai",
            "azure-foundry",
            "google-gemini", 
            "amazon-bedrock",
            "azure-ai-speech"
        }
        
        actual_providers = {provider.value for provider in ModelProvider}
        assert actual_providers == expected_providers
    
    def test_from_str_valid(self):
        """Test converting valid strings to ModelProvider enum."""
        test_cases = [
            ("azure-openai", ModelProvider.AZURE_OPENAI),
            ("azure-foundry", ModelProvider.AZURE_FOUNDRY),
            ("google-gemini", ModelProvider.GOOGLE_GEMINI),
            ("amazon-bedrock", ModelProvider.AMAZON_BEDROCK),
            ("azure-ai-speech", ModelProvider.AZURE_AI_SPEECH)
        ]
        
        for string_val, expected_enum in test_cases:
            result = ModelProvider.from_str(string_val)
            assert result == expected_enum
            assert result.value == string_val
    
    def test_from_str_invalid(self):
        """Test converting invalid strings raises ValueError."""
        invalid_providers = [
            "invalid-provider",
            "openai",
            "azure",
            "google",
            "amazon",
            "",
            "AZURE_OPENAI",  # Wrong case
            "azure_openai"   # Wrong separator
        ]
        
        for invalid_provider in invalid_providers:
            with pytest.raises(ValueError, match=f"Invalid provider: {invalid_provider}"):
                ModelProvider.from_str(invalid_provider)


class TestAIModel:
    """Test the AIModel class."""
    
    def test_ai_model_creation(self):
        """Test creating an AIModel instance."""
        capabilities = [Capability.CHAT_CONVERSATION, Capability.STREAMING]
        
        model = AIModel(
            name="gpt-4o-mini",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=128000,
            capabilities=capabilities,
            family="gpt-4"
        )
        
        assert model.name == "gpt-4o-mini"
        assert model.provider == ModelProvider.AZURE_OPENAI
        assert model.directory == "test-dir"
        assert model.region == "eastus"
        assert model.input_tokens == 128000
        assert model.capabilities == capabilities
        assert model.family == "gpt-4"
    
    def test_ai_model_creation_without_family(self):
        """Test creating an AIModel instance without family."""
        model = AIModel(
            name="gpt-4o-mini",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=128000,
            capabilities=[Capability.CHAT_CONVERSATION]
        )
        
        assert model.family is None
    
    def test_ai_model_creation_empty_capabilities(self):
        """Test creating an AIModel instance with empty capabilities."""
        model = AIModel(
            name="test-model",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=1000
        )
        
        assert model.capabilities == []
    
    def test_from_dict_complete(self):
        """Test creating AIModel from complete dictionary."""
        data = {
            "name": "gpt-4o-mini",
            "provider": "azure-openai",
            "directory": "test-dir",
            "region": "eastus",
            "inputTokens": 128000,
            "capabilities": ["chat-conversation", "streaming"],
            "family": "gpt-4"
        }
        
        model = AIModel.from_dict(data)
        
        assert model.name == "gpt-4o-mini"
        assert model.provider == ModelProvider.AZURE_OPENAI
        assert model.directory == "test-dir"
        assert model.region == "eastus"
        assert model.input_tokens == 128000
        assert model.capabilities == [Capability.CHAT_CONVERSATION, Capability.STREAMING]
        assert model.family == "gpt-4"
    
    def test_from_dict_minimal(self):
        """Test creating AIModel from minimal dictionary."""
        data = {
            "name": "test-model",
            "provider": "azure-openai",
            "directory": "test-dir",
            "region": "eastus",
            "inputTokens": 1000
        }
        
        model = AIModel.from_dict(data)
        
        assert model.name == "test-model"
        assert model.provider == ModelProvider.AZURE_OPENAI
        assert model.capabilities == []
        assert model.family is None
    
    def test_from_dict_invalid_provider(self):
        """Test creating AIModel with invalid provider raises error."""
        data = {
            "name": "test-model",
            "provider": "invalid-provider",
            "directory": "test-dir",
            "region": "eastus",
            "inputTokens": 1000
        }
        
        with pytest.raises(ValueError, match="Invalid provider: invalid-provider"):
            AIModel.from_dict(data)
    
    def test_from_dict_invalid_capability(self):
        """Test creating AIModel with invalid capability raises error."""
        data = {
            "name": "test-model",
            "provider": "azure-openai",
            "directory": "test-dir",
            "region": "eastus",
            "inputTokens": 1000,
            "capabilities": ["chat-conversation", "invalid-capability"]
        }
        
        with pytest.raises(ValueError, match="Invalid capability: invalid-capability"):
            AIModel.from_dict(data)
    
    def test_has_capability(self):
        """Test checking if model has specific capability."""
        model = AIModel(
            name="test-model",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=1000,
            capabilities=[Capability.CHAT_CONVERSATION, Capability.STREAMING]
        )
        
        assert model.has_capability(Capability.CHAT_CONVERSATION) is True
        assert model.has_capability(Capability.STREAMING) is True
        assert model.has_capability(Capability.TEXT_EMBEDDING) is False
        assert model.has_capability(Capability.IMAGE_GENERATION) is False
    
    def test_has_all_capabilities(self):
        """Test checking if model has all specified capabilities."""
        model = AIModel(
            name="test-model",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=1000,
            capabilities=[
                Capability.CHAT_CONVERSATION,
                Capability.STREAMING,
                Capability.IMAGE_RECOGNITION
            ]
        )
        
        # Test with capabilities the model has
        assert model.has_all_capabilities([Capability.CHAT_CONVERSATION]) is True
        assert model.has_all_capabilities([
            Capability.CHAT_CONVERSATION,
            Capability.STREAMING
        ]) is True
        assert model.has_all_capabilities([
            Capability.CHAT_CONVERSATION,
            Capability.STREAMING,
            Capability.IMAGE_RECOGNITION
        ]) is True
        
        # Test with capabilities the model doesn't have
        assert model.has_all_capabilities([Capability.TEXT_EMBEDDING]) is False
        assert model.has_all_capabilities([
            Capability.CHAT_CONVERSATION,
            Capability.TEXT_EMBEDDING
        ]) is False
        
        # Test with empty list
        assert model.has_all_capabilities([]) is True
    
    def test_to_dict_complete(self):
        """Test converting AIModel to dictionary with all fields."""
        model = AIModel(
            name="gpt-4o-mini",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=128000,
            capabilities=[Capability.CHAT_CONVERSATION, Capability.STREAMING],
            family="gpt-4"
        )
        
        result = model.to_dict()
        
        expected = {
            "name": "gpt-4o-mini",
            "provider": "azure-openai",
            "directory": "test-dir",
            "region": "eastus",
            "input_tokens": 128000,
            "capabilities": ["chat-conversation", "streaming"],
            "family": "gpt-4"
        }
        
        assert result == expected
    
    def test_to_dict_minimal(self):
        """Test converting AIModel to dictionary without optional fields."""
        model = AIModel(
            name="test-model",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=1000
        )
        
        result = model.to_dict()
        
        expected = {
            "name": "test-model",
            "provider": "azure-openai",
            "directory": "test-dir",
            "region": "eastus",
            "input_tokens": 1000,
            "capabilities": []
        }
        
        assert result == expected
        assert "family" not in result
    
    def test_model_equality(self):
        """Test AIModel equality comparison."""
        model1 = AIModel(
            name="gpt-4o-mini",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=128000,
            capabilities=[Capability.CHAT_CONVERSATION]
        )
        
        model2 = AIModel(
            name="gpt-4o-mini",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=128000,
            capabilities=[Capability.CHAT_CONVERSATION]
        )
        
        model3 = AIModel(
            name="different-model",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=128000,
            capabilities=[Capability.CHAT_CONVERSATION]
        )
        
        assert model1 == model2
        assert model1 != model3
