"""
Integration tests for flow_api.

These tests verify that different components work together correctly.
"""

import pytest
import base64
import requests
from unittest.mock import patch, Mo<PERSON>

class TestEndToEndWorkflows:
    """Test complete end-to-end workflows."""
    
    def test_chat_conversation_workflow(self, mock_env_vars, mock_models_data, mock_api_responses):
        """Test complete chat conversation workflow."""
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient, Capability
            # Mock da resposta HTTP
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_api_responses['chat_completion']
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            # Test workflow
            client = FlowAPIClient()
            # 1. List available models
            models = client.list_models()
            assert len(models) > 0
            # 2. Select model by capability
            client.with_capability(Capability.CHAT_CONVERSATION)
            # 3. Send chat request
            response = client.send(
                system_prompt="You are a helpful assistant.",
                user_prompt="What is the capital of Brazil?"
            )
            assert "choices" in response
            assert response["choices"][0]["message"]["content"] == "This is a test response from the AI model."
            # 5. Get content directly
            content = client.get_answer(user_prompt="Another question")
            assert content == "This is a test response from the AI model."
            print("MODELS MOCK PARA TESTE:", mock_models_data)
            print("MOCK MODELS DATA:", mock_models_data)
    
    def test_text_embedding_workflow(self, mock_env_vars, mock_models_data, mock_api_responses):
        """Test complete text embedding workflow."""
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient
            # Mock da resposta HTTP
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_api_responses['text_embedding']
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            # Test workflow
            client = FlowAPIClient()
            # 1. Generate embedding with auto model selection
            response = client.generate_embedding(
                text="The quick brown fox jumps over the lazy dog"
            )
            assert "data" in response
            assert len(response["data"]) > 0
            assert "embedding" in response["data"][0]
            assert isinstance(response["data"][0]["embedding"], list)
            # 2. Generate embedding with specific model
            response2 = client.generate_embedding(
                text="Another text to embed",
                model="text-embedding-ada-002",
                encoding_format="float"
            )
            assert "data" in response2
            print("MODELS MOCK PARA TESTE:", mock_models_data)
            print("MOCK MODELS DATA:", mock_models_data)
    
    def test_image_generation_workflow(self, mock_env_vars, mock_models_data, mock_api_responses):
        """Test complete image generation workflow."""
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient
            # Mock da resposta HTTP
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_api_responses['image_generation']
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            # Test workflow
            client = FlowAPIClient()
            # 1. Generate image with auto model selection
            response = client.generate_image(
                prompt="A serene mountain landscape at sunset"
            )
            assert "data" in response
            assert len(response["data"]) > 0
            assert "url" in response["data"][0]
            # 2. Generate image with specific options
            response2 = client.generate_image(
                prompt="A futuristic cityscape",
                model="dall-e-3",
                size="512x512",
                quality="hd",
                n=1
            )
            assert "data" in response2
            print("MODELS MOCK PARA TESTE:", mock_models_data)
            print("MOCK MODELS DATA:", mock_models_data)
    
    def test_speech_to_text_workflow(self, mock_env_vars, mock_models_data, mock_api_responses):
        """Test complete speech-to-text workflow."""
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient
            # Mock da resposta HTTP
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_api_responses['speech_to_text']
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            # Test workflow
            client = FlowAPIClient()
            # 1. Create test audio data
            test_audio = b"fake audio data for testing"
            audio_base64 = base64.b64encode(test_audio).decode('utf-8')
            # 2. Transcribe audio with auto model selection
            response = client.generate_transcript(audio_data=audio_base64)
            assert "combinedPhrases" in response
            # 3. Verify response structure
            assert len(response["combinedPhrases"]) > 0
            assert "text" in response["combinedPhrases"][0]
            # 4. Transcribe with specific options
            response2 = client.generate_transcript(
                audio_data=audio_base64,
                model="whisper",
                language="en",
                response_format="verbose_json"
            )
            assert "combinedPhrases" in response2
            print("MODELS MOCK PARA TESTE:", mock_models_data)
            print("MOCK MODELS DATA:", mock_models_data)
    
    def test_streaming_workflow(self, mock_env_vars, mock_models_data):
        """Test complete streaming workflow."""
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient, Capability
            # Mock da resposta HTTP para streaming
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.raise_for_status.return_value = None
            mock_response.iter_lines.return_value = [
                b'data: {"choices":[{"delta":{"content":"The"}}]}',
                b'data: {"choices":[{"delta":{"content":" capital"}}]}',
                b'data: {"choices":[{"delta":{"content":" of"}}]}',
                b'data: {"choices":[{"delta":{"content":" Brazil"}}]}',
                b'data: {"choices":[{"delta":{"content":" is"}}]}',
                b'data: {"choices":[{"delta":{"content":" Brasilia"}}]}',
                b'data: [DONE]'
            ]
            mock_post.return_value = mock_response
            # Test workflow
            client = FlowAPIClient()
            # 1. Select streaming-capable model
            client.with_capabilities([Capability.CHAT_CONVERSATION, Capability.STREAMING])
            # 2. Send streaming request
            chunks = list(client.get_stream_answer(
                system_prompt="You are a helpful assistant.",
                user_prompt="What is the capital of Brazil?"
            ))
            # 3. Verify streaming response
            expected_chunks = ["The", " capital", " of", " Brazil", " is", " Brasilia"]
            assert chunks == expected_chunks
            # 4. Reconstruct full response
            full_response = "".join(chunks)
            assert full_response == "The capital of Brazil is Brasilia"
            print("MODELS MOCK PARA TESTE:", mock_models_data)
            print("MOCK MODELS DATA:", mock_models_data)


class TestErrorHandling:
    """Test error handling across components."""
    
    def test_network_error_handling(self, mock_env_vars, mock_models_data):
        """Test handling of network errors."""
        from flow_api.exceptions.client_error import ClientError
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient
            # Simulate network error
            mock_post.side_effect = requests.exceptions.ConnectionError("Network error")
            client = FlowAPIClient()
            client.with_model("gpt-4o-mini")
            with pytest.raises(ClientError, match="Request error"):
                client.send(user_prompt="Test message")
    
    def test_http_error_handling(self, mock_env_vars, mock_models_data):
        """Test handling of HTTP errors."""
        from flow_api.exceptions.client_error import ClientError
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient
            # Simulate HTTP error
            mock_response = Mock()
            mock_response.status_code = 400
            mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("400 Bad Request")
            mock_post.return_value = mock_response
            client = FlowAPIClient()
            client.with_model("gpt-4o-mini")
            with pytest.raises(ClientError, match="HTTP error"):
                client.send(user_prompt="Test message")
    
    def test_invalid_response_handling(self, mock_env_vars, mock_models_data):
        """Test handling of invalid API responses."""
        from flow_api.exceptions.client_error import ClientError
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient
            # Simulate invalid response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"invalid": "response"}  # Missing 'choices'
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            client = FlowAPIClient()
            client.with_model("gpt-4o-mini")
            with pytest.raises(ClientError, match="API response does not contain 'choices'"):
                client.send(user_prompt="Test message")


class TestModelCompatibility:
    """Test compatibility across different model types and providers."""
    
    def test_azure_openai_models(self, mock_env_vars, mock_models_data, mock_api_responses):
        """Test Azure OpenAI model compatibility."""
        from flow_api.exceptions.client_error import ClientError
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient
            # Setup HTTP response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_api_responses['chat_completion']
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            client = FlowAPIClient()
            # Test with specific Azure OpenAI model
            client.with_model("gpt-4o-mini")
            current_model = client.get_current_model()
            assert current_model is not None
            print("MODELOS AZUREOPENAI:", mock_models_data['supportedModels']['azureOpenai'])
    
    def test_capability_based_selection(self, mock_env_vars, mock_models_data, mock_api_responses):
        """Test capability-based model selection works across providers."""
        from flow_api.exceptions.client_error import ClientError
        with patch('flow_api.adapters.outbound.auth.token_manager.TokenManager.get_cached_models', return_value=mock_models_data), \
             patch('requests.post') as mock_post:
            from flow_api import FlowAPIClient, Capability
            # Setup HTTP response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = mock_api_responses['chat_completion']
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            client = FlowAPIClient()
            # Test different capability selections
            capabilities_to_test = [
                Capability.CHAT_CONVERSATION,
                Capability.TEXT_EMBEDDING,
                Capability.IMAGE_GENERATION,
                Capability.SPEECH_TO_TEXT
            ]
            for capability in capabilities_to_test:
                try:
                    client.with_capability(capability)
                    current_model = client.get_current_model()
                    assert current_model is not None
                except ClientError:
                    pass
            print("MODELS MOCK PARA TESTE:", mock_models_data)
            print("MOCK MODELS DATA:", mock_models_data)
