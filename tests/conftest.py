"""
Pytest configuration and fixtures for flow_api tests.
"""

import pytest
import os
import json
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any, List

from flow_api.core.models import AIModel, ModelProvider
from flow_api.core.capabilities import Capability
from flow_api.core.model_registry import ModelRegistry
from flow_api.adapters.outbound.auth.token_manager import TokenManager


@pytest.fixture
def mock_env_vars():
    """Mock environment variables for testing."""
    env_vars = {
        'FLOW_API_CLIENT_ID': 'test_client_id',
        'FLOW_API_CLIENT_SECRET': 'test_client_secret',
        'FLOW_API_APP_TO_ACCESS': 'test_app',
        'FLOW_API_TENANT': 'test_tenant',
        'FLOW_API_BASE_URL': 'https://test-api.example.com',
        'FLOW_API_CONSOLE_LOG_LEVEL': 'OFF',
        'FLOW_API_FILE_LOG_LEVEL': 'OFF'
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars


@pytest.fixture
def mock_models_data():
    data = {
        "supportedModels": {
            "azureOpenai": [
                {
                    "directory": "flow-core-llm-eaus-ca",
                    "name": "gpt-4o",
                    "region": "eastus",
                    "inputTokens": 128000,
                    "capabilities": [
                        "streaming",
                        "system-instruction",
                        "chat-conversation",
                        "image-recognition"
                    ]
                },
                {
                    "directory": "flow-core-llm-eaus-ca",
                    "name": "gpt-4o-mini",
                    "region": "eastus",
                    "inputTokens": 128000,
                    "capabilities": [
                        "streaming",
                        "system-instruction",
                        "chat-conversation",
                        "image-recognition"
                    ]
                },
                {
                    "directory": "flow-core-llm-eaus-ca",
                    "name": "text-embedding-ada-002",
                    "region": "eastus",
                    "inputTokens": 8192,
                    "capabilities": [
                        "text-embedding"
                    ]
                },
                {
                    "directory": "flow-core-llm-eaus-ca",
                    "name": "dall-e-3",
                    "region": "eastus",
                    "inputTokens": 1000,
                    "capabilities": [
                        "image-generation"
                    ]
                },
                {
                    "directory": "flow-core-llm-eaus-ca",
                    "name": "whisper",
                    "region": "eastus",
                    "inputTokens": 10000,
                    "capabilities": [
                        "speech-to-text"
                    ]
                }
            ]
        }
    }
    print("FIXTURE MOCK MODELS DATA:", data)
    return data


@pytest.fixture
def sample_ai_models():
    """Sample AI models for testing."""
    return [
        AIModel(
            name="gpt-4o-mini",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=128000,
            capabilities=[
                Capability.STREAMING,
                Capability.SYSTEM_INSTRUCTION,
                Capability.CHAT_CONVERSATION,
                Capability.IMAGE_RECOGNITION
            ]
        ),
        AIModel(
            name="text-embedding-ada-002",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=8192,
            capabilities=[Capability.STREAMING, Capability.TEXT_EMBEDDING]
        ),
        AIModel(
            name="dall-e-3",
            provider=ModelProvider.AZURE_OPENAI,
            directory="test-dir",
            region="eastus",
            input_tokens=1000,
            capabilities=[Capability.IMAGE_GENERATION]
        ),
        AIModel(
            name="whisper",
            provider=ModelProvider.AZURE_AI_SPEECH,
            directory="test-dir",
            region="eastus",
            input_tokens=0,
            capabilities=[Capability.SPEECH_TO_TEXT]
        )
    ]


@pytest.fixture
def mock_api_responses():
    """Mock API responses for different endpoints."""
    return {
        'chat_completion': {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "This is a test response from the AI model."
                    },
                    "finish_reason": "stop"
                }
            ],
            "model": "gpt-4o-mini",
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 15,
                "total_tokens": 25
            }
        },
        'text_embedding': {
            "data": [
                {
                    "embedding": [0.1, 0.2, 0.3, 0.4, 0.5] * 300,  # 1536 dimensions
                    "index": 0
                }
            ],
            "model": "text-embedding-ada-002",
            "usage": {
                "prompt_tokens": 5,
                "total_tokens": 5
            }
        },
        'image_generation': {
            "data": [
                {
                    "url": "https://example.com/generated-image.jpg",
                    "revised_prompt": "A detailed image of a futuristic cityscape"
                }
            ]
        },
        'speech_to_text': {
            "combinedPhrases": [
                {
                    "text": "This is a test transcription of the audio file."
                }
            ],
            "phrases": [
                {
                    "text": "This is a test transcription of the audio file.",
                    "speaker": 1,
                    "confidence": 0.95
                }
            ]
        },
        'streaming_chunk': {
            "choices": [
                {
                    "delta": {
                        "content": "test chunk"
                    },
                    "finish_reason": None
                }
            ]
        }
    }


@pytest.fixture
def mock_token_response():
    """Mock token response for authentication."""
    return {
        "access_token": "test_access_token_12345",
        "token_type": "Bearer",
        "expires_in": 3600
    }


@pytest.fixture
def mock_requests_session():
    """Mock requests session for HTTP calls."""
    session = Mock()
    response = Mock()
    response.status_code = 200
    response.raise_for_status.return_value = None
    session.post.return_value = response
    session.get.return_value = response
    return session


@pytest.fixture
def mock_auth_and_models():
    """Mock authentication and model loading."""
    with patch('requests.post') as mock_post, \
         patch('requests.get') as mock_get:

        # Mock token response
        token_response = Mock()
        token_response.status_code = 200
        token_response.json.return_value = {
            "access_token": "test_access_token_12345",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        token_response.raise_for_status.return_value = None

        # Mock models response
        models_response = Mock()
        models_response.status_code = 200
        models_response.json.return_value = {
            "models": {
                "name": "test",
                "allCapabilities": ["chat-conversation", "text-embedding"],
                "supportedModels": {
                    "azureOpenai": [
                        {
                            "directory": "test-dir",
                            "name": "gpt-4o-mini",
                            "region": "eastus",
                            "inputTokens": 128000,
                            "capabilities": ["chat-conversation"]
                        }
                    ]
                }
            }
        }
        models_response.raise_for_status.return_value = None

        # Return different responses based on URL
        def post_side_effect(url, **kwargs):
            if 'token' in url:
                return token_response
            else:
                return models_response

        def get_side_effect(url, **kwargs):
            return models_response

        mock_post.side_effect = post_side_effect
        mock_get.side_effect = get_side_effect

        yield {
            'post': mock_post,
            'get': mock_get,
            'token_response': token_response,
            'models_response': models_response
        }


@pytest.fixture
def mock_requests_post():
    """Mock requests.post for HTTP calls with proper token response."""
    with patch('requests.post') as mock_post:
        # Mock token response
        token_response = Mock()
        token_response.status_code = 200
        token_response.json.return_value = {
            "access_token": "test_access_token_12345",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        token_response.raise_for_status.return_value = None

        # Mock models response
        models_response = Mock()
        models_response.status_code = 200
        models_response.json.return_value = {
            "models": {
                "name": "test",
                "allCapabilities": ["chat-conversation", "text-embedding"],
                "supportedModels": {
                    "azureOpenai": [
                        {
                            "directory": "test-dir",
                            "name": "gpt-4o-mini",
                            "region": "eastus",
                            "inputTokens": 128000,
                            "capabilities": ["chat-conversation"]
                        }
                    ]
                }
            }
        }
        models_response.raise_for_status.return_value = None

        # Return different responses based on URL
        def side_effect(url, **kwargs):
            if 'token' in url:
                return token_response
            else:
                return models_response

        mock_post.side_effect = side_effect
        yield mock_post


@pytest.fixture(autouse=True)
def reset_singletons():
    """Reset singleton instances between tests."""
    # Reset ModelRegistry singleton
    if hasattr(ModelRegistry, '_instance'):
        ModelRegistry._instance = None
    
    # Reset Config singleton  
    from flow_api.config import Config
    if hasattr(Config, '_instance'):
        Config._instance = None
    
    yield
    
    # Clean up after test
    if hasattr(ModelRegistry, '_instance'):
        ModelRegistry._instance = None
    if hasattr(Config, '_instance'):
        Config._instance = None


@pytest.fixture
def mock_file_operations():
    """Mock file operations for cache and token management."""
    with patch('builtins.open', create=True) as mock_open, \
         patch('os.path.exists') as mock_exists, \
         patch('os.makedirs') as mock_makedirs, \
         patch('json.load') as mock_json_load, \
         patch('json.dump') as mock_json_dump:
        
        mock_exists.return_value = True
        mock_json_load.return_value = {}
        
        yield {
            'open': mock_open,
            'exists': mock_exists,
            'makedirs': mock_makedirs,
            'json_load': mock_json_load,
            'json_dump': mock_json_dump
        }


@pytest.fixture(autouse=True)
def reset_model_registry_singleton():
    ModelRegistry._instance = None


@pytest.fixture(autouse=True)
def reset_token_manager_singleton():
    TokenManager._instance = None
