#!/usr/bin/env python3
"""
Setup script for flow_api package.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="flow_api",
    version="1.1.0",
    author="<PERSON><PERSON><PERSON>",
    author_email="<EMAIL>",
    description="A Python library for interacting with Flow AI APIs",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/ciandt/flow_api",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.8",
    install_requires=[
        "requests>=2.25.0",
        "tenacity>=8.0.0",
        "python-dotenv>=0.15.0",
    ],
    package_data={
        "flow_api": ["resources/*.json"],
    },
)