[tool:pytest]
# Pytest configuration file

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    network: Tests that require network access
    auth: Tests that require authentication

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (if pytest-cov is installed)
# addopts = --cov=flow_api --cov-report=html --cov-report=term-missing

# Logging
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
