#!/usr/bin/env python3
"""
Image generation example with flow_api.

This example shows how to generate images using AI models.
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flow_api import FlowAPIClient

# Load environment variables
load_dotenv()


def main():
    """Simple image generation example."""
    print("=== Flow API - Image Generation Example ===\n")
    
    # Initialize client
    client = FlowAPIClient()
    
    # Find image generation models
    models = client.list_models()
    image_models = [m for m in models if 'image-generation' in m.get('capabilities', [])]
    
    if not image_models:
        print("❌ No image generation models available")
        return
    
    # Use first available image generation model
    model_name = image_models[0]['name']
    client.with_model(model_name)

    # Set the image generation capability
    from flow_api.core.capabilities import Capability
    client.with_capability(Capability.IMAGE_GENERATION)
    
    print(f"🤖 Using model: {model_name}")
    
    # Image generation prompt
    prompt = "A serene mountain landscape at sunset with a crystal clear lake reflecting the orange and pink sky, painted in watercolor style"
    
    try:
        print(f"🎨 Generating image with prompt: {prompt}")
        
        response = client.send(
            prompt=prompt,
            size="1024x1024",
            quality="standard",
            style="vivid",
            response_format="url",
            n=1
        )
        
        print("✅ Image generation completed!")

        # Check different possible response formats
        if 'data' in response:
            print(f"📥 Generated {len(response['data'])} image(s)")
            for i, image_data in enumerate(response['data']):
                if 'url' in image_data:
                    # Dall-E 3 format
                    print(f"🖼️ Image {i+1} URL: {image_data['url']}")
                elif 'b64_json' in image_data:
                    print(f"🖼️ Image {i+1}: Base64 data received")

                # Show revised prompt if available
                if 'revised_prompt' in image_data:
                    print(f"� Revised prompt: {image_data['revised_prompt']}")
        else:
            print("📥 Response format may vary by provider")
            print(f"📥 Response: {response}")
        
    except Exception as e:
        print(f"❌ Error generating image: {e}")
    
    print("\n💡 Tips:")
    print("- Be descriptive in your prompts for better results")
    print("- Available sizes may vary by provider (e.g., 256x256, 512x512, 1024x1024)")
    print("- Quality options: 'standard' or 'hd' (if supported)")
    print("- You can generate multiple images by setting n > 1")


if __name__ == "__main__":
    main()
