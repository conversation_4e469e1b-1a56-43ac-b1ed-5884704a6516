#!/usr/bin/env python3
"""
Image recognition examples with flow_api.

This example shows image analysis with URL and base64 data.
"""

import os
import sys
import base64
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flow_api import FlowAPIClient

# Load environment variables
load_dotenv()


def example_1_image_url_with_system_prompt():
    """Example 1: Image recognition with URL and system prompt."""
    print("=== Example 1: Image Recognition with URL ===\n")
    
    # Initialize client
    client = FlowAPIClient()
    
    # Find image recognition models
    models = client.list_models()
    image_models = [m for m in models if 'image-recognition' in m.get('capabilities', [])]
    
    if not image_models:
        print("❌ No image recognition models available")
        return
    
    # Use first available image recognition model
    model_name = image_models[0]['name']
    client.with_model(model_name)
    
    print(f"🤖 Using model: {model_name}")
    
    # Image URL (example - replace with actual image URL)
    image_url = "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
    
    # System prompt for detailed analysis
    system_prompt = """You are an expert image analyst. Provide a detailed description of the image including:
    - Main objects and subjects
    - Colors and lighting
    - Setting and environment
    - Any text visible
    - Overall mood or atmosphere"""
    
    try:
        # Create messages with image URL
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Please analyze this image in detail."
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            }
        ]
        
        response = client.get_answer(
            system_prompt=system_prompt,
            user_prompt=messages
        )
        
        print(f"🖼️ Image URL: {image_url}")
        print(f"📥 Analysis: {response}")
        print("✅ URL image recognition completed\n")
    except Exception as e:
        print(f"❌ Error: {e}\n")


def example_2_image_base64():
    """Example 2: Image recognition with base64 data."""
    print("=== Example 2: Image Recognition with Base64 ===\n")
    
    # Initialize client
    client = FlowAPIClient()
    
    # Find image recognition models
    models = client.list_models()
    image_models = [m for m in models if 'image-recognition' in m.get('capabilities', [])]
    
    if not image_models:
        print("❌ No image recognition models available")
        return
    
    # Use first available image recognition model
    model_name = image_models[0]['name']
    client.with_model(model_name)
    
    print(f"🤖 Using model: {model_name}")
    
    # Create a simple base64 image (1x1 red pixel PNG)
    # In real usage, you would load an actual image file
    red_pixel_png = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    try:
        # Create messages with base64 image
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "What color is this image?"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{red_pixel_png}"
                        }
                    }
                ]
            }
        ]
        
        response = client.get_answer(
            user_prompt=messages
        )
        
        print(f"🖼️ Image: 1x1 red pixel (base64)")
        print(f"📥 Analysis: {response}")
        print("✅ Base64 image recognition completed\n")
    except Exception as e:
        print(f"❌ Error: {e}\n")


def load_image_as_base64(image_path):
    """Helper function to load image file as base64."""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        print(f"❌ Error loading image: {e}")
        return None


def main():
    """Run all image recognition examples."""
    print("=== Flow API - Image Recognition Examples ===\n")
    
    example_1_image_url_with_system_prompt()
    example_2_image_base64()
    
    print("=== All Examples Completed ===")
    print("\n💡 Tips:")
    print("- For local images, use load_image_as_base64() function")
    print("- Supported formats: JPEG, PNG, GIF, WebP")
    print("- Maximum image size varies by provider")


if __name__ == "__main__":
    main()
