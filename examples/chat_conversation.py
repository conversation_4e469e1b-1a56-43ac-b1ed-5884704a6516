#!/usr/bin/env python3
"""
Chat conversation examples with flow_api.

This example shows basic chat, streaming, and system prompts.
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flow_api import FlowAPIClient

# Load environment variables
load_dotenv()

def check_connection():
    """Basic connection check before making requests, for better UX."""
    print("\n=== Connection Check ===\n")
    
    client = FlowAPIClient()
    
    # Check connection at application start
    print("🔧 Initializing application...")
    connection_result = client.check_connection()
    
    if connection_result['status'] == 'connected':
        print(f"✅ Initialization complete: {connection_result['message']}")
        
        # Show user that app is ready
        print("🎉 Application ready! You can now make requests without delays.\n")
    else:
        print(f"❌ Initialization failed: {connection_result['message']}")
        print("🔧 Please check your configuration and try again.")
        print("   When you try send a request, we will automatically check the connection.")


def example_1_basic_chat():
    """Example 1: Basic chat with model selection."""
    print("=== Example 1: Basic Chat ===\n")
    
    # Initialize client
    client = FlowAPIClient()
    
    # List available chat models
    models = client.list_models()
    chat_models = [m for m in models if 'chat-conversation' in m.get('capabilities', [])]
    
    if not chat_models:
        print("❌ No chat models available")
        return
    
    # Use first available chat model
    # model_name = chat_models[0]['name']
    model_name = "amazon.nova-lite"
    client.with_model(model_name)
    
    print(f"🤖 Using model: {model_name}")
    
    # Simple chat
    try:
        response = client.get_answer(
            user_prompt="What are the three laws of robotics?"
        )
        print(f"📤 Question: What are the three laws of robotics?")
        print(f"📥 Response: {response}")
        print("✅ Basic chat completed\n")
    except Exception as e:
        print(f"❌ Error: {e}\n")


def example_2_streaming_chat():
    """Example 2: Chat with streaming."""
    print("=== Example 2: Streaming Chat ===\n")

    # Initialize client
    client = FlowAPIClient()

    # Find a model that supports streaming
    models = client.list_models()
    streaming_models = [m for m in models if 'streaming' in m.get('capabilities', [])
                       and 'chat-conversation' in m.get('capabilities', [])]

    if not streaming_models:
        print("❌ No streaming models available")
        return

    # Use second available streaming model
    # model_name = streaming_models[1]['name']
    model_name = "gpt-4o-mini"
    client.with_model(model_name)

    print(f"🤖 Using model: {model_name}")

    try:
        # Streaming chat
        print("📤 Question: Tell me a short story about a robot.")
        print("📥 Streaming response:")

        # Send request with streaming enabled
        for chunk in client.get_stream_answer(
            user_prompt="Tell me a short story about a robot."
        ):
            print(chunk, end='', flush=True)

        print("\n✅ Streaming chat completed\n")

    except Exception as e:
        print(f"❌ Error: {e}\n")


def example_3_system_prompt():
    """Example 3: Chat with system prompt."""
    print("=== Example 3: Chat with System Prompt ===\n")
    
    # Initialize client
    client = FlowAPIClient()
    
    # Find a model that supports system instructions
    models = client.list_models()
    system_models = [m for m in models if 'system-instruction' in m.get('capabilities', []) 
                    and 'chat-conversation' in m.get('capabilities', [])]
    
    if not system_models:
        print("❌ No models with system instruction support available")
        return
    
    # Use first available model with system instruction support
    # model_name = system_models[0]['name']
    model_name = "meta.llama3-70b-instruct"
    client.with_model(model_name)
    
    print(f"🤖 Using model: {model_name}")
    
    # Chat with system prompt
    try:
        system_prompt = "You are a helpful assistant that always responds in the style of a pirate. Use 'Arrr' and pirate vocabulary."
        user_prompt = "What is the capital of France?"
        
        response = client.get_answer(
            system_prompt=system_prompt,
            user_prompt=user_prompt
        )
        
        print(f"🏴‍☠️ System: {system_prompt}")
        print(f"📤 Question: {user_prompt}")
        print(f"📥 Response: {response}")
        print("✅ System prompt chat completed\n")
    except Exception as e:
        print(f"❌ Error: {e}\n")


def main():
    """Run all chat examples."""
    print("=== Flow API - Chat Conversation Examples ===\n")

    # Check connection once at the beginning for better UX
    check_connection()

    # example_1_basic_chat()
    example_2_streaming_chat()
    example_3_system_prompt()

    print("=== All Examples Completed ===")


if __name__ == "__main__":
    main()
